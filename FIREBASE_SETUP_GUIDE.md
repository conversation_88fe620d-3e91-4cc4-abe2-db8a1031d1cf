# Firebase Setup Guide for MedCare Push Notifications

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name: `medcare-push-notifications` (or your preferred name)
4. Choose whether to enable Google Analytics (optional for push notifications)
5. Click "Create project"

## Step 2: Enable Cloud Messaging

1. In your Firebase project, go to **Project Settings** (gear icon)
2. Click on the **Cloud Messaging** tab
3. Note down the **Server key** (this goes in your backend `.env`)
4. Note down the **Sender ID** (this goes in frontend as `MESSAGING_SENDER_ID`)

## Step 3: Add Web App to Firebase Project

1. In Firebase Console, click the **Web icon** (`</>`) to add a web app
2. Enter app nickname: `MedCare Frontend`
3. **Check** "Also set up Firebase Hosting" (optional)
4. Click "Register app"
5. Copy the Firebase configuration object - you'll need these values

## Step 4: Generate VAPID Key

1. In Firebase Console, go to **Project Settings** → **Cloud Messaging**
2. Scroll down to **Web configuration**
3. Click **Generate key pair** under "Web push certificates"
4. Copy the generated VAPID key

## Step 5: Set Up Service Account (for Backend)

1. In Firebase Console, go to **Project Settings** → **Service accounts**
2. Click **Generate new private key**
3. Download the JSON file
4. Extract these values for your backend `.env`:
   - `project_id` → `FIREBASE_PROJECT_ID`
   - `client_email` → `FIREBASE_CLIENT_EMAIL`
   - `private_key` → `FIREBASE_PRIVATE_KEY`

## Step 6: Configure Frontend Environment Variables

Create `frontend/.env.local` with these values from your Firebase config:

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001/api/v1

# Firebase Configuration (replace with your actual values)
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyC...your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:abcdef123456
NEXT_PUBLIC_FIREBASE_VAPID_KEY=BG7s...your-vapid-key

# Application Configuration
NEXT_PUBLIC_APP_NAME=MedCare
NEXT_PUBLIC_APP_VERSION=1.0.0
```

## Step 7: Configure Backend Environment Variables

Add to `backend/.env`:

```env
# Firebase Configuration (for server-side FCM)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-private-key-here\n-----END PRIVATE KEY-----"
```

## Step 8: Update Service Worker Configuration

Update `frontend/public/firebase-messaging-sw.js` with your Firebase config:

```javascript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};
```

## Step 9: Test the Setup

1. Start your backend: `cd backend && npm run start:dev`
2. Start your frontend: `cd frontend && npm run dev`
3. Go to the reminders page and toggle push notifications
4. Click the "Test" button for push notifications

## Security Notes

- **Frontend variables are PUBLIC**: All `NEXT_PUBLIC_*` variables are visible to users
- **Backend variables are PRIVATE**: Keep your private key and service account details secure
- **VAPID key is safe to expose**: It's designed to be public for web push
- **API key is safe for frontend**: Firebase API keys are designed to be public

## Troubleshooting

### Common Issues:

1. **"Permission denied"**: User hasn't granted notification permission
2. **"Service worker not found"**: Check if `firebase-messaging-sw.js` is in `/public`
3. **"Invalid VAPID key"**: Make sure VAPID key is correctly copied
4. **"Token registration failed"**: Check Firebase project configuration

### Testing Commands:

```bash
# Test FCM setup
cd backend && node scripts/test-fcm-setup.js

# Check if service worker is registered
# Open browser dev tools → Application → Service Workers
```

## Next Steps After Setup

1. Create the FCM tokens table in Supabase
2. Test push notifications end-to-end
3. Integrate with reminder system
4. Test on different devices/browsers
