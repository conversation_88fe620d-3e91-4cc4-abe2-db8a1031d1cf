import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    console.log('🛡️ JwtAuthGuard.canActivate called:', {
      url: request.url,
      method: request.method,
      hasAuthHeader: !!authHeader,
      authHeaderPreview: authHeader?.substring(0, 20) + '...',
      userAgent: request.headers['user-agent']?.substring(0, 50) + '...'
    });

    if (!authHeader) {
      console.error('❌ No Authorization header found in request');
      throw new UnauthorizedException('No authorization header');
    }

    if (!authHeader.startsWith('Bearer ')) {
      console.error('❌ Invalid Authorization header format:', authHeader.substring(0, 20));
      throw new UnauthorizedException('Invalid authorization header format');
    }

    const token = authHeader.substring(7);
    console.log('🔑 Extracted token:', {
      tokenLength: token.length,
      tokenPreview: token.substring(0, 20) + '...'
    });

    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any) {
    console.log('🔍 JwtAuthGuard.handleRequest called:', {
      hasError: !!err,
      hasUser: !!user,
      hasInfo: !!info,
      errorMessage: err?.message,
      infoMessage: info?.message,
      userId: user?.id,
      userRole: user?.role
    });

    if (err || !user) {
      console.error('❌ JWT authentication failed:', {
        error: err?.message,
        info: info?.message
      });
      throw err || new UnauthorizedException('Authentication failed');
    }

    console.log('✅ JWT authentication successful for user:', {
      id: user.id,
      email: user.email,
      role: user.role
    });

    return user;
  }
}
