import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { SupabaseService } from '../../config/supabase.service';

export interface FCMTokenData {
  userId: string;
  token: string;
  deviceType: 'web' | 'android' | 'ios';
  userAgent?: string;
  createdAt: Date;
  lastUsed: Date;
}

export interface FCMNotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  data?: Record<string, string>;
  clickAction?: string;
}

export interface FCMSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
  failedTokens?: string[];
}

@Injectable()
export class FCMService implements OnModuleInit {
  private readonly logger = new Logger(FCMService.name);
  private isInitialized = false;

  constructor(private readonly supabaseService: SupabaseService) {}

  async onModuleInit() {
    await this.initializeFirebase();
  }

  private async initializeFirebase(): Promise<void> {
    try {
      // Check if Firebase is already initialized
      if (admin.apps.length > 0) {
        this.isInitialized = true;
        this.logger.log('Firebase Admin SDK already initialized');
        return;
      }

      // Initialize Firebase Admin SDK
      const firebaseConfig = {
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      };

      if (!firebaseConfig.projectId || !firebaseConfig.clientEmail || !firebaseConfig.privateKey) {
        this.logger.warn('Firebase configuration missing - FCM push notifications will be disabled');
        return;
      }

      admin.initializeApp({
        credential: admin.credential.cert(firebaseConfig),
      });

      this.isInitialized = true;
      this.logger.log('Firebase Admin SDK initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Firebase Admin SDK:', error);
    }
  }

  /**
   * Store FCM token for a user
   */
  async storeUserToken(tokenData: FCMTokenData): Promise<boolean> {
    try {
      const supabase = this.supabaseService.getAdminClient();
      
      // Check if token already exists
      const { data: existingToken } = await supabase
        .from('fcm_tokens')
        .select('id')
        .eq('user_id', tokenData.userId)
        .eq('token', tokenData.token)
        .single();

      if (existingToken) {
        // Update last used timestamp
        const { error } = await supabase
          .from('fcm_tokens')
          .update({ 
            last_used: tokenData.lastUsed.toISOString(),
            user_agent: tokenData.userAgent 
          })
          .eq('id', existingToken.id);

        if (error) throw error;
        this.logger.log(`Updated existing FCM token for user ${tokenData.userId}`);
      } else {
        // Insert new token
        const { error } = await supabase
          .from('fcm_tokens')
          .insert({
            user_id: tokenData.userId,
            token: tokenData.token,
            device_type: tokenData.deviceType,
            user_agent: tokenData.userAgent,
            created_at: tokenData.createdAt.toISOString(),
            last_used: tokenData.lastUsed.toISOString(),
          });

        if (error) throw error;
        this.logger.log(`Stored new FCM token for user ${tokenData.userId}`);
      }

      return true;
    } catch (error) {
      this.logger.error('Failed to store FCM token:', error);
      return false;
    }
  }

  /**
   * Get all active tokens for a user
   */
  async getUserTokens(userId: string): Promise<string[]> {
    try {
      const supabase = this.supabaseService.getAdminClient();
      
      const { data, error } = await supabase
        .from('fcm_tokens')
        .select('token')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) throw error;

      return data?.map(row => row.token) || [];
    } catch (error) {
      this.logger.error('Failed to get user tokens:', error);
      return [];
    }
  }

  /**
   * Send push notification to a single token
   */
  async sendToToken(token: string, payload: FCMNotificationPayload): Promise<FCMSendResult> {
    if (!this.isInitialized) {
      return { success: false, error: 'FCM not initialized' };
    }

    try {
      const message = {
        token,
        notification: {
          title: payload.title,
          body: payload.body,
          ...(payload.icon && { icon: payload.icon }),
          ...(payload.image && { image: payload.image }),
        },
        webpush: {
          notification: {
            title: payload.title,
            body: payload.body,
            icon: payload.icon || '/icons/icon-192x192.png',
            badge: payload.badge || '/icons/badge-72x72.png',
            ...(payload.clickAction && { click_action: payload.clickAction }),
          },
          fcm_options: {
            link: payload.clickAction || '/',
          },
        },
        ...(payload.data && { data: payload.data }),
      };

      const response = await admin.messaging().send(message);
      
      this.logger.log(`FCM notification sent successfully: ${response}`);
      return { success: true, messageId: response };
    } catch (error) {
      this.logger.error('Failed to send FCM notification:', error);
      
      // Handle invalid token errors
      if (error.code === 'messaging/invalid-registration-token' || 
          error.code === 'messaging/registration-token-not-registered') {
        await this.removeInvalidToken(token);
      }
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Send push notification to multiple tokens
   */
  async sendToMultipleTokens(tokens: string[], payload: FCMNotificationPayload): Promise<FCMSendResult> {
    if (!this.isInitialized) {
      return { success: false, error: 'FCM not initialized' };
    }

    if (tokens.length === 0) {
      return { success: false, error: 'No tokens provided' };
    }

    try {
      const message = {
        notification: {
          title: payload.title,
          body: payload.body,
          ...(payload.icon && { icon: payload.icon }),
          ...(payload.image && { image: payload.image }),
        },
        webpush: {
          notification: {
            title: payload.title,
            body: payload.body,
            icon: payload.icon || '/icons/icon-192x192.png',
            badge: payload.badge || '/icons/badge-72x72.png',
            ...(payload.clickAction && { click_action: payload.clickAction }),
          },
          fcm_options: {
            link: payload.clickAction || '/',
          },
        },
        ...(payload.data && { data: payload.data }),
        tokens,
      };

      const response = await admin.messaging().sendEachForMulticast(message);
      
      // Handle failed tokens
      const failedTokens: string[] = [];
      if (response.failureCount > 0) {
        response.responses.forEach((resp, idx) => {
          if (!resp.success) {
            const token = tokens[idx];
            failedTokens.push(token);
            
            // Remove invalid tokens
            if (resp.error?.code === 'messaging/invalid-registration-token' || 
                resp.error?.code === 'messaging/registration-token-not-registered') {
              this.removeInvalidToken(token);
            }
          }
        });
      }

      this.logger.log(`FCM multicast sent: ${response.successCount} success, ${response.failureCount} failed`);
      
      return {
        success: response.successCount > 0,
        messageId: `${response.successCount}/${tokens.length} sent`,
        failedTokens: failedTokens.length > 0 ? failedTokens : undefined,
      };
    } catch (error) {
      this.logger.error('Failed to send FCM multicast:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send push notification to a user (all their tokens)
   */
  async sendToUser(userId: string, payload: FCMNotificationPayload): Promise<FCMSendResult> {
    const tokens = await this.getUserTokens(userId);
    
    if (tokens.length === 0) {
      this.logger.warn(`No FCM tokens found for user ${userId}`);
      return { success: false, error: 'No FCM tokens found for user' };
    }

    return this.sendToMultipleTokens(tokens, payload);
  }

  /**
   * Remove invalid token from database
   */
  private async removeInvalidToken(token: string): Promise<void> {
    try {
      const supabase = this.supabaseService.getAdminClient();
      
      const { error } = await supabase
        .from('fcm_tokens')
        .update({ is_active: false })
        .eq('token', token);

      if (error) throw error;
      
      this.logger.log(`Marked invalid FCM token as inactive: ${token.substring(0, 20)}...`);
    } catch (error) {
      this.logger.error('Failed to remove invalid token:', error);
    }
  }

  /**
   * Remove user token
   */
  async removeUserToken(userId: string, token: string): Promise<boolean> {
    try {
      const supabase = this.supabaseService.getAdminClient();
      
      const { error } = await supabase
        .from('fcm_tokens')
        .update({ is_active: false })
        .eq('user_id', userId)
        .eq('token', token);

      if (error) throw error;
      
      this.logger.log(`Removed FCM token for user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to remove user token:', error);
      return false;
    }
  }

  /**
   * Clean up old/inactive tokens
   */
  async cleanupOldTokens(daysOld: number = 30): Promise<void> {
    try {
      const supabase = this.supabaseService.getAdminClient();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      
      const { error } = await supabase
        .from('fcm_tokens')
        .update({ is_active: false })
        .lt('last_used', cutoffDate.toISOString());

      if (error) throw error;
      
      this.logger.log(`Cleaned up FCM tokens older than ${daysOld} days`);
    } catch (error) {
      this.logger.error('Failed to cleanup old tokens:', error);
    }
  }

  /**
   * Get FCM service status
   */
  getStatus(): { initialized: boolean; available: boolean } {
    return {
      initialized: this.isInitialized,
      available: this.isInitialized && admin.apps.length > 0,
    };
  }
}
