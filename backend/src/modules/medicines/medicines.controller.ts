import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { MedicinesService } from './medicines.service';
import { CreateMedicineDto, UpdateMedicineDto, MedicineQueryDto } from './dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User, Medicine } from '../../common/types';

@ApiTags('medicines')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('medicines')
export class MedicinesController {
  constructor(private readonly medicinesService: MedicinesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new medicine' })
  @ApiResponse({ status: 201, description: 'Medicine created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async create(
    @Body() createMedicineDto: CreateMedicineDto,
    @CurrentUser() currentUser: User,
  ) {
    const medicine = await this.medicinesService.create(createMedicineDto, currentUser);
    return this.transformMedicineResponse(medicine);
  }

  @Get()
  @ApiOperation({ summary: 'Get all medicines' })
  @ApiQuery({ name: 'patient_id', required: false, description: 'Filter by patient ID' })
  @ApiQuery({ name: 'prescription_id', required: false, description: 'Filter by prescription ID' })
  @ApiQuery({ name: 'name', required: false, description: 'Filter by medicine name' })
  @ApiQuery({ name: 'status', required: false, enum: ['active', 'completed', 'all'], description: 'Filter by status' })
  @ApiResponse({ status: 200, description: 'Medicines retrieved successfully' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findAll(
    @Query() query: MedicineQueryDto,
    @CurrentUser() currentUser: User,
  ) {
    const medicines = await this.medicinesService.findAll(query, currentUser);
    return medicines.map(medicine => this.transformMedicineResponse(medicine));
  }

  @Get('patient/:patientId')
  @ApiOperation({ summary: 'Get medicines for a specific patient' })
  @ApiResponse({ status: 200, description: 'Patient medicines retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Patient not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findByPatient(
    @Param('patientId', ParseUUIDPipe) patientId: string,
    @CurrentUser() currentUser: User,
  ) {
    const medicines = await this.medicinesService.findByPatient(patientId, currentUser);
    return medicines.map(medicine => this.transformMedicineResponse(medicine));
  }

  @Get('prescription/:prescriptionId')
  @ApiOperation({ summary: 'Get medicines for a specific prescription' })
  @ApiResponse({ status: 200, description: 'Prescription medicines retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Prescription not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findByPrescription(
    @Param('prescriptionId', ParseUUIDPipe) prescriptionId: string,
    @CurrentUser() currentUser: User,
  ) {
    const medicines = await this.medicinesService.findByPrescription(prescriptionId, currentUser);
    return medicines.map(medicine => this.transformMedicineResponse(medicine));
  }

  @Get('next-dose')
  @ApiOperation({ summary: 'Get the next medicine dose to be taken' })
  @ApiResponse({ status: 200, description: 'Next medicine dose retrieved successfully' })
  @Roles('patient')
  async getNextDose(@CurrentUser() user: User): Promise<{
    medicine: Medicine;
    nextDoseTime: string;
    isOverdue: boolean;
  } | null> {
    return this.medicinesService.getNextDose(user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get medicine by ID' })
  @ApiResponse({ status: 200, description: 'Medicine retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Medicine not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    const medicine = await this.medicinesService.findOne(id, currentUser);
    return this.transformMedicineResponse(medicine);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update medicine' })
  @ApiResponse({ status: 200, description: 'Medicine updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Medicine not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMedicineDto: UpdateMedicineDto,
    @CurrentUser() currentUser: User,
  ) {
    const medicine = await this.medicinesService.update(id, updateMedicineDto, currentUser);
    return this.transformMedicineResponse(medicine);
  }

  @Post(':id/take')
  @ApiOperation({ summary: 'Mark medicine as taken' })
  @ApiResponse({ status: 200, description: 'Medicine marked as taken successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Medicine not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async markAsTaken(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() data: { takenTime?: string; notes?: string },
    @CurrentUser() currentUser: User,
  ) {
    return this.medicinesService.markAsTaken(id, data, currentUser);
  }

  @Post(':id/miss')
  @ApiOperation({ summary: 'Mark medicine as missed' })
  @ApiResponse({ status: 200, description: 'Medicine marked as missed successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Medicine not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async markAsMissed(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() data: { reason?: string; notes?: string },
    @CurrentUser() currentUser: User,
  ) {
    return this.medicinesService.markAsMissed(id, data, currentUser);
  }

  @Post(':id/skip')
  @ApiOperation({ summary: 'Skip medicine dose' })
  @ApiResponse({ status: 200, description: 'Medicine dose skipped successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Medicine not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async skipDose(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() data: { reason?: string; notes?: string },
    @CurrentUser() currentUser: User,
  ) {
    return this.medicinesService.skipDose(id, data, currentUser);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete medicine' })
  @ApiResponse({ status: 200, description: 'Medicine deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Medicine not found' })
  @Roles('doctor', 'hospital', 'admin')
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    await this.medicinesService.remove(id, currentUser);
    return { message: 'Medicine deleted successfully' };
  }

  /**
   * Transform medicine response from snake_case to camelCase for frontend compatibility
   */
  private transformMedicineResponse(medicine: any) {
    return {
      id: medicine.id,
      name: medicine.name,
      dosage: medicine.dosage,
      frequency: medicine.frequency,
      duration: medicine.duration,
      instructions: medicine.instructions,
      sideEffects: medicine.side_effects,
      startDate: medicine.start_date,
      endDate: medicine.end_date,
      prescriptionId: medicine.prescription_id,
      patientId: medicine.patient_id,
      createdAt: medicine.created_at,
      updatedAt: medicine.updated_at,
      prescription: medicine.prescription,
      patient: medicine.patient,
      adherenceRecords: medicine.adherence_records || [],
    };
  }
}
