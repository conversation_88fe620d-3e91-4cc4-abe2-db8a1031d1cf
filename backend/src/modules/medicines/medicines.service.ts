import { Injectable, NotFoundException, BadRequestException, ForbiddenException, Logger } from '@nestjs/common';
import { SupabaseService } from '../../config/supabase.service';
import { User, Medicine } from '../../common/types';
import { CreateMedicineDto, UpdateMedicineDto, MedicineQueryDto } from './dto';

@Injectable()
export class MedicinesService {
  private readonly logger = new Logger(MedicinesService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  async create(createMedicineDto: CreateMedicineDto, currentUser: User): Promise<Medicine> {
    const supabase = this.supabaseService.getAdminClient();

    // Verify prescription exists and user has access to it
    const { data: prescription, error: prescriptionError } = await supabase
      .from('prescriptions')
      .select('id, patient_id, doctor_id')
      .eq('id', createMedicineDto.prescription_id)
      .single();

    if (prescriptionError || !prescription) {
      throw new NotFoundException('Prescription not found');
    }

    // Check if user has permission to add medicines to this prescription
    if (currentUser.role === 'patient' && prescription.patient_id !== currentUser.id) {
      throw new ForbiddenException('You can only add medicines to your own prescriptions');
    }

    if (currentUser.role === 'doctor' && prescription.doctor_id !== currentUser.id) {
      throw new ForbiddenException('You can only add medicines to your own prescriptions');
    }

    // Verify patient exists
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('id')
      .eq('id', createMedicineDto.patient_id)
      .single();

    if (patientError || !patient) {
      throw new NotFoundException('Patient not found');
    }

    // Ensure patient matches prescription
    if (prescription.patient_id !== createMedicineDto.patient_id) {
      throw new BadRequestException('Patient ID does not match prescription');
    }

    // Validate dates
    const startDate = new Date(createMedicineDto.start_date);
    const endDate = new Date(createMedicineDto.end_date);
    
    if (endDate <= startDate) {
      throw new BadRequestException('End date must be after start date');
    }

    const { data, error } = await supabase
      .from('medicines')
      .insert({
        name: createMedicineDto.name,
        dosage: createMedicineDto.dosage,
        frequency: createMedicineDto.frequency,
        duration: createMedicineDto.duration,
        instructions: createMedicineDto.instructions,
        side_effects: createMedicineDto.side_effects,
        start_date: createMedicineDto.start_date,
        end_date: createMedicineDto.end_date,
        prescription_id: createMedicineDto.prescription_id,
        patient_id: createMedicineDto.patient_id,
      })
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to create medicine: ${error.message}`);
    }

    // Automatically create reminders for the new medicine
    try {
      await this.createRemindersForMedicine(data);
    } catch (reminderError) {
      this.logger.warn(`Failed to create reminders for medicine ${data.id}:`, reminderError);
      // Don't fail the medicine creation if reminder creation fails
    }

    return data;
  }

  async findAll(query: MedicineQueryDto, currentUser: User): Promise<Medicine[]> {
    const supabase = this.supabaseService.getAdminClient();
    
    let dbQuery = supabase
      .from('medicines')
      .select(`
        *,
        prescription:prescriptions(id, file_url, status),
        patient:patients(id, users!patients_id_fkey(name, email))
      `);

    // Apply role-based filtering
    if (currentUser.role === 'patient') {
      dbQuery = dbQuery.eq('patient_id', currentUser.id);
    } else if (currentUser.role === 'doctor') {
      // Doctors can see medicines for their patients
      const { data: patientIds } = await supabase
        .from('patients')
        .select('id')
        .eq('assigned_doctor_id', currentUser.id);

      const ids = patientIds?.map(p => p.id) || [];
      if (ids.length > 0) {
        dbQuery = dbQuery.in('patient_id', ids);
      } else {
        // No patients assigned, return empty result
        return [];
      }
    }

    // Apply query filters
    if (query.patient_id) {
      // Check if user has permission to view this patient's medicines
      if (currentUser.role === 'patient' && query.patient_id !== currentUser.id) {
        throw new ForbiddenException('You can only view your own medicines');
      }
      dbQuery = dbQuery.eq('patient_id', query.patient_id);
    }

    if (query.prescription_id) {
      dbQuery = dbQuery.eq('prescription_id', query.prescription_id);
    }

    if (query.name) {
      dbQuery = dbQuery.ilike('name', `%${query.name}%`);
    }

    // Apply status filter
    const today = new Date().toISOString().split('T')[0];
    if (query.status === 'active') {
      dbQuery = dbQuery.lte('start_date', today).gte('end_date', today);
    } else if (query.status === 'completed') {
      dbQuery = dbQuery.lt('end_date', today);
    }

    const { data, error } = await dbQuery.order('created_at', { ascending: false });

    if (error) {
      throw new BadRequestException(`Failed to fetch medicines: ${error.message}`);
    }

    return data || [];
  }

  async findOne(id: string, currentUser: User): Promise<Medicine> {
    const supabase = this.supabaseService.getAdminClient();

    const { data, error } = await supabase
      .from('medicines')
      .select(`
        *,
        prescription:prescriptions(id, file_url, status, doctor_id),
        patient:patients(id, users!patients_id_fkey(name, email), assigned_doctor_id)
      `)
      .eq('id', id)
      .single();

    if (error || !data) {
      throw new NotFoundException(`Medicine with ID ${id} not found`);
    }

    // Check permissions
    if (currentUser.role === 'patient' && data.patient_id !== currentUser.id) {
      throw new ForbiddenException('You can only view your own medicines');
    } else if (currentUser.role === 'doctor') {
      const hasAccess = data.prescription?.doctor_id === currentUser.id || 
                       data.patient?.assigned_doctor_id === currentUser.id;
      if (!hasAccess) {
        throw new ForbiddenException('You can only view medicines for your patients');
      }
    }

    return data;
  }

  async update(id: string, updateMedicineDto: UpdateMedicineDto, currentUser: User): Promise<Medicine> {
    const supabase = this.supabaseService.getAdminClient();

    // Get existing medicine with permission check
    const existingMedicine = await this.findOne(id, currentUser);

    // Validate dates if provided
    if (updateMedicineDto.start_date || updateMedicineDto.end_date) {
      const startDate = new Date(updateMedicineDto.start_date || existingMedicine.start_date);
      const endDate = new Date(updateMedicineDto.end_date || existingMedicine.end_date);
      
      if (endDate <= startDate) {
        throw new BadRequestException('End date must be after start date');
      }
    }

    const { data, error } = await supabase
      .from('medicines')
      .update({
        ...(updateMedicineDto.name && { name: updateMedicineDto.name }),
        ...(updateMedicineDto.dosage && { dosage: updateMedicineDto.dosage }),
        ...(updateMedicineDto.frequency && { frequency: updateMedicineDto.frequency }),
        ...(updateMedicineDto.duration && { duration: updateMedicineDto.duration }),
        ...(updateMedicineDto.instructions && { instructions: updateMedicineDto.instructions }),
        ...(updateMedicineDto.side_effects !== undefined && { side_effects: updateMedicineDto.side_effects }),
        ...(updateMedicineDto.start_date && { start_date: updateMedicineDto.start_date }),
        ...(updateMedicineDto.end_date && { end_date: updateMedicineDto.end_date }),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to update medicine: ${error.message}`);
    }

    return data;
  }

  async markAsTaken(id: string, data: { takenTime?: string; notes?: string }, currentUser: User): Promise<any> {
    const medicine = await this.findOne(id, currentUser);

    // Patients can only mark their own medicines as taken
    if (currentUser.role === 'patient' && medicine.patient_id !== currentUser.id) {
      throw new ForbiddenException('Patients can only mark their own medicines as taken');
    }

    // Create adherence record using the adherence service
    const supabase = this.supabaseService.getAdminClient();

    // Create adherence record directly
    const { data: adherenceRecord, error } = await supabase
      .from('adherence_records')
      .insert({
        patient_id: medicine.patient_id,
        medicine_id: id,
        scheduled_time: data.takenTime || new Date().toISOString(),
        taken_time: data.takenTime || new Date().toISOString(),
        status: 'taken',
        notes: data.notes || 'Marked as taken from patient dashboard',
      })
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to record medicine intake: ${error.message}`);
    }

    // Calculate next dose time based on medicine frequency
    const nextDoseTime = this.calculateNextDoseTime(medicine.frequency, new Date(data.takenTime || new Date().toISOString()));

    return {
      message: 'Medicine marked as taken successfully',
      adherenceRecord,
      nextDoseTime: nextDoseTime?.toISOString()
    };
  }

  async markAsMissed(id: string, data: { reason?: string; notes?: string }, currentUser: User): Promise<any> {
    const medicine = await this.findOne(id, currentUser);

    // Patients can only mark their own medicines as missed
    if (currentUser.role === 'patient' && medicine.patient_id !== currentUser.id) {
      throw new ForbiddenException('Patients can only mark their own medicines as missed');
    }

    // Create adherence record
    const supabase = this.supabaseService.getAdminClient();

    const { data: adherenceRecord, error } = await supabase
      .from('adherence_records')
      .insert({
        patient_id: medicine.patient_id,
        medicine_id: id,
        scheduled_time: new Date().toISOString(),
        status: 'missed',
        notes: data.notes || data.reason || 'Marked as missed from patient dashboard',
      })
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to record missed dose: ${error.message}`);
    }

    return { message: 'Medicine marked as missed successfully', adherenceRecord };
  }

  async skipDose(id: string, data: { reason?: string; notes?: string }, currentUser: User): Promise<any> {
    const medicine = await this.findOne(id, currentUser);

    // Patients can only skip their own medicines
    if (currentUser.role === 'patient' && medicine.patient_id !== currentUser.id) {
      throw new ForbiddenException('Patients can only skip their own medicines');
    }

    // Create adherence record
    const supabase = this.supabaseService.getAdminClient();

    const { data: adherenceRecord, error } = await supabase
      .from('adherence_records')
      .insert({
        patient_id: medicine.patient_id,
        medicine_id: id,
        scheduled_time: new Date().toISOString(),
        status: 'skipped',
        notes: data.notes || data.reason || 'Dose skipped from patient dashboard',
      })
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to record skipped dose: ${error.message}`);
    }

    return { message: 'Medicine dose skipped successfully', adherenceRecord };
  }

  async remove(id: string, currentUser: User): Promise<void> {
    // Only doctors, hospitals, and admins can delete medicines
    if (!['doctor', 'hospital', 'admin'].includes(currentUser.role)) {
      throw new ForbiddenException('Only doctors, hospitals, and admins can delete medicines');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Get existing medicine with permission check
    await this.findOne(id, currentUser);

    const { error } = await supabase
      .from('medicines')
      .delete()
      .eq('id', id);

    if (error) {
      throw new BadRequestException(`Failed to delete medicine: ${error.message}`);
    }
  }

  async findByPatient(patientId: string, currentUser: User): Promise<Medicine[]> {
    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== patientId) {
      throw new ForbiddenException('You can only view your own medicines');
    }

    return this.findAll({ patient_id: patientId }, currentUser);
  }

  async findByPrescription(prescriptionId: string, currentUser: User): Promise<Medicine[]> {
    return this.findAll({ prescription_id: prescriptionId }, currentUser);
  }

  /**
   * Create medicine records from AI-extracted medicine names
   * This method is called automatically after prescription upload and medicine extraction
   */
  async createMedicinesFromExtractedNames(
    extractedMedicineNames: string[],
    prescriptionId: string,
    patientId: string,
    currentUser: User,
  ): Promise<Medicine[]> {
    if (!extractedMedicineNames || extractedMedicineNames.length === 0) {
      return [];
    }

    const supabase = this.supabaseService.getAdminClient();
    const createdMedicines: Medicine[] = [];

    // Set default dates (start today, end in 30 days)
    const startDate = new Date().toISOString().split('T')[0];
    const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    for (const medicineName of extractedMedicineNames) {
      try {
        // Parse medicine name to extract dosage if possible
        const parsedMedicine = this.parseMedicineName(medicineName);

        const { data, error } = await supabase
          .from('medicines')
          .insert({
            name: parsedMedicine.name,
            dosage: parsedMedicine.dosage,
            frequency: 'Once daily', // Default frequency
            duration: '30', // Default 30 days
            instructions: 'Take as directed by your doctor',
            start_date: startDate,
            end_date: endDate,
            prescription_id: prescriptionId,
            patient_id: patientId,
            is_active: true,
          })
          .select()
          .single();

        if (error) {
          console.error(`Failed to create medicine record for "${medicineName}":`, error);
          continue; // Skip this medicine and continue with others
        }

        createdMedicines.push(data);

        // Automatically create reminders for the extracted medicine
        try {
          await this.createRemindersForMedicine(data);
        } catch (reminderError) {
          this.logger.warn(`Failed to create reminders for extracted medicine ${data.id}:`, reminderError);
          // Don't fail the medicine creation if reminder creation fails
        }
      } catch (error) {
        console.error(`Error processing medicine "${medicineName}":`, error);
        continue; // Skip this medicine and continue with others
      }
    }

    return createdMedicines;
  }

  /**
   * Parse medicine name to extract name and dosage
   * Examples:
   * - "TAB. GLIZID M XR 60 MG" -> { name: "GLIZID M XR", dosage: "60 MG" }
   * - "LINERO 5 MG TABLET" -> { name: "LINERO", dosage: "5 MG" }
   */
  private parseMedicineName(fullName: string): { name: string; dosage: string } {
    const cleanName = fullName.trim();

    // Remove common prefixes
    let name = cleanName
      .replace(/^(TAB\.|TABLET|CAP\.|CAPSULE|SYR\.|SYRUP)\s*/i, '')
      .trim();

    // Extract dosage pattern (number + unit)
    const dosageMatch = name.match(/(\d+(?:\.\d+)?\s*(?:MG|MCG|G|ML|IU|%|UNITS?))\s*(TABLET|TAB|CAPSULE|CAP|ML)?$/i);

    let dosage = 'As prescribed';
    if (dosageMatch) {
      dosage = dosageMatch[1].toUpperCase();
      // Remove dosage from name
      name = name.replace(dosageMatch[0], '').trim();
    }

    // Remove trailing form indicators
    name = name.replace(/\s+(TABLET|TAB|CAPSULE|CAP|SYRUP|SYR)$/i, '').trim();

    return {
      name: name || cleanName, // Fallback to original if parsing fails
      dosage,
    };
  }

  /**
   * Create automatic reminders for a newly created medicine
   */
  private async createRemindersForMedicine(medicine: Medicine): Promise<void> {
    const supabase = this.supabaseService.getAdminClient();

    // Parse the frequency to determine reminder times
    const reminderTimes = this.parseMedicationFrequency(medicine.frequency);

    if (reminderTimes.length === 0) {
      this.logger.warn(`No reminder times could be parsed for medicine ${medicine.id} with frequency: ${medicine.frequency}`);
      return;
    }

    // Get patient's preferred reminder type (default to 'sms')
    const { data: patientData } = await supabase
      .from('patients')
      .select('preferred_reminder_type')
      .eq('id', medicine.patient_id)
      .single();

    const reminderType = patientData?.preferred_reminder_type || 'sms';

    // Create reminders for each time
    for (const reminderTime of reminderTimes) {
      try {
        // Calculate the first reminder time (today if it's in the future, tomorrow otherwise)
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const reminderDateTime = new Date(today);
        reminderDateTime.setHours(reminderTime.hour, reminderTime.minute, 0, 0);

        // If the time has already passed today, schedule for tomorrow
        if (reminderDateTime <= now) {
          reminderDateTime.setDate(reminderDateTime.getDate() + 1);
        }

        const { error: reminderError } = await supabase
          .from('reminders')
          .insert({
            patient_id: medicine.patient_id,
            medicine_id: medicine.id,
            scheduled_time: reminderDateTime.toISOString(),
            reminder_type: reminderType,
            message: `Time to take your ${medicine.name}${medicine.dosage ? ` (${medicine.dosage})` : ''}`,
            is_recurring: true,
            recurrence_pattern: 'daily',
            recurrence_interval: 1,
            end_date: medicine.end_date,
            status: 'pending',
            is_active: true,
          });

        if (reminderError) {
          this.logger.error(`Failed to create reminder for medicine ${medicine.id} at ${reminderTime.hour}:${reminderTime.minute}:`, reminderError);
        } else {
          this.logger.log(`Created reminder for medicine ${medicine.name} at ${reminderTime.hour}:${reminderTime.minute}`);
        }
      } catch (error) {
        this.logger.error(`Error creating reminder for medicine ${medicine.id}:`, error);
      }
    }
  }

  /**
   * Parse medication frequency to determine reminder times
   */
  private parseMedicationFrequency(frequency: string): Array<{ hour: number; minute: number }> {
    const freq = frequency.toLowerCase().trim();

    // Common frequency patterns
    if (freq.includes('once daily') || freq.includes('once a day') || freq.includes('1 time') || freq === 'daily') {
      return [{ hour: 9, minute: 0 }]; // 9:00 AM
    }

    if (freq.includes('twice daily') || freq.includes('twice a day') || freq.includes('2 times') || freq.includes('bid')) {
      return [
        { hour: 9, minute: 0 },   // 9:00 AM
        { hour: 21, minute: 0 }   // 9:00 PM
      ];
    }

    if (freq.includes('three times') || freq.includes('3 times') || freq.includes('tid') || freq.includes('thrice')) {
      return [
        { hour: 8, minute: 0 },   // 8:00 AM
        { hour: 14, minute: 0 },  // 2:00 PM
        { hour: 20, minute: 0 }   // 8:00 PM
      ];
    }

    if (freq.includes('four times') || freq.includes('4 times') || freq.includes('qid')) {
      return [
        { hour: 8, minute: 0 },   // 8:00 AM
        { hour: 12, minute: 0 },  // 12:00 PM
        { hour: 16, minute: 0 },  // 4:00 PM
        { hour: 20, minute: 0 }   // 8:00 PM
      ];
    }

    if (freq.includes('every 6 hours') || freq.includes('q6h')) {
      return [
        { hour: 6, minute: 0 },   // 6:00 AM
        { hour: 12, minute: 0 },  // 12:00 PM
        { hour: 18, minute: 0 },  // 6:00 PM
        { hour: 0, minute: 0 }    // 12:00 AM
      ];
    }

    if (freq.includes('every 8 hours') || freq.includes('q8h')) {
      return [
        { hour: 8, minute: 0 },   // 8:00 AM
        { hour: 16, minute: 0 },  // 4:00 PM
        { hour: 0, minute: 0 }    // 12:00 AM
      ];
    }

    if (freq.includes('every 12 hours') || freq.includes('q12h')) {
      return [
        { hour: 8, minute: 0 },   // 8:00 AM
        { hour: 20, minute: 0 }   // 8:00 PM
      ];
    }

    // Default to once daily if we can't parse the frequency
    this.logger.warn(`Could not parse medication frequency: "${frequency}". Defaulting to once daily.`);
    return [{ hour: 9, minute: 0 }]; // 9:00 AM
  }

  /**
   * Calculate the next dose time based on frequency and last taken time
   */
  private calculateNextDoseTime(frequency: string, lastTakenTime: Date): Date | null {
    const freq = frequency.toLowerCase().trim();
    const nextDose = new Date(lastTakenTime);

    // Calculate interval based on frequency
    if (freq.includes('once daily') || freq.includes('once a day') || freq.includes('1 time') || freq === 'daily') {
      nextDose.setDate(nextDose.getDate() + 1);
      nextDose.setHours(9, 0, 0, 0); // Next day at 9:00 AM
    } else if (freq.includes('twice daily') || freq.includes('twice a day') || freq.includes('2 times') || freq.includes('bid')) {
      nextDose.setHours(nextDose.getHours() + 12); // Every 12 hours
    } else if (freq.includes('three times') || freq.includes('3 times') || freq.includes('tid') || freq.includes('thrice')) {
      nextDose.setHours(nextDose.getHours() + 8); // Every 8 hours
    } else if (freq.includes('four times') || freq.includes('4 times') || freq.includes('qid')) {
      nextDose.setHours(nextDose.getHours() + 6); // Every 6 hours
    } else if (freq.includes('every 6 hours') || freq.includes('q6h')) {
      nextDose.setHours(nextDose.getHours() + 6);
    } else if (freq.includes('every 8 hours') || freq.includes('q8h')) {
      nextDose.setHours(nextDose.getHours() + 8);
    } else if (freq.includes('every 12 hours') || freq.includes('q12h')) {
      nextDose.setHours(nextDose.getHours() + 12);
    } else {
      // Default to once daily
      nextDose.setDate(nextDose.getDate() + 1);
      nextDose.setHours(9, 0, 0, 0);
    }

    return nextDose;
  }

  /**
   * Get the next medicine dose to be taken for a patient
   */
  async getNextDose(currentUser: User): Promise<{
    medicine: Medicine;
    nextDoseTime: string;
    isOverdue: boolean;
  } | null> {
    if (currentUser.role !== 'patient') {
      throw new ForbiddenException('Only patients can get next dose information');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Get all active medicines for the patient
    const { data: medicines, error } = await supabase
      .from('medicines')
      .select('*')
      .eq('patient_id', currentUser.id)
      .eq('is_active', true)
      .gte('end_date', new Date().toISOString().split('T')[0]);

    if (error) {
      throw new BadRequestException(`Failed to fetch medicines: ${error.message}`);
    }

    if (!medicines || medicines.length === 0) {
      return null;
    }

    let nextMedicine: Medicine | null = null;
    let earliestNextDose: Date | null = null;

    // For each medicine, calculate the next dose time
    for (const medicine of medicines) {
      // Get the last adherence record for this medicine
      const { data: lastRecord } = await supabase
        .from('adherence_records')
        .select('taken_time')
        .eq('medicine_id', medicine.id)
        .eq('status', 'taken')
        .order('taken_time', { ascending: false })
        .limit(1)
        .single();

      let nextDoseTime: Date | null = null;

      if (lastRecord?.taken_time) {
        // Calculate next dose based on last taken time
        const calculatedTime = this.calculateNextDoseTime(medicine.frequency, new Date(lastRecord.taken_time));
        nextDoseTime = calculatedTime || new Date(); // Fallback to current time if calculation fails
      } else {
        // No previous dose, use the first scheduled time based on frequency
        const reminderTimes = this.parseMedicationFrequency(medicine.frequency);
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        // Find the next scheduled time today or tomorrow
        let foundToday = false;
        for (const time of reminderTimes) {
          const scheduledTime = new Date(today);
          scheduledTime.setHours(time.hour, time.minute, 0, 0);

          if (scheduledTime > now) {
            nextDoseTime = scheduledTime;
            foundToday = true;
            break;
          }
        }

        if (!foundToday) {
          // All times for today have passed, use first time tomorrow
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);
          tomorrow.setHours(reminderTimes[0].hour, reminderTimes[0].minute, 0, 0);
          nextDoseTime = tomorrow;
        }
      }

      // Check if this is the earliest next dose
      if (nextDoseTime && (!earliestNextDose || nextDoseTime < earliestNextDose)) {
        earliestNextDose = nextDoseTime;
        nextMedicine = medicine;
      }
    }

    if (!nextMedicine || !earliestNextDose) {
      return null;
    }

    const isOverdue = earliestNextDose < new Date();

    return {
      medicine: nextMedicine,
      nextDoseTime: earliestNextDose.toISOString(),
      isOverdue,
    };
  }
}
