import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Logger, UseFilters, UseGuards } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Server, Socket } from 'socket.io';
import { NotificationsService } from './notifications.service';
import { WsAuthGuard, WsRolesGuard, WsRoles } from '../../common/guards/ws-auth.guard';
import {
  NotificationType,
  NotificationRole,
  SendNotificationDto,
  BroadcastNotificationDto,
  NotificationHistoryQueryDto,
} from './dto';
import { User } from '../../common/types';

// WebSocket Exception Filter
import { Catch, ArgumentsHost } from '@nestjs/common';
import { BaseWsExceptionFilter, WsException } from '@nestjs/websockets';

@Catch(WsException)
export class WebSocketExceptionFilter extends BaseWsExceptionFilter {
  catch(exception: WsException, host: ArgumentsHost) {
    const client = host.switchToWs().getClient<Socket>();
    const error = exception.getError();
    const details = error instanceof Object ? { ...error } : { message: error };
    
    client.emit('error', {
      id: client.id,
      rid: Date.now(),
      ...details,
    });
  }
}

@WebSocketGateway({
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/',
})
@UseFilters(WebSocketExceptionFilter)
@UseGuards(WsAuthGuard)
export class NotificationsGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(NotificationsGateway.name);

  constructor(private readonly notificationsService: NotificationsService) {}

  // Connection Handlers
  async handleConnection(client: Socket) {
    try {
      const user = client.data.user as User;
      if (!user) {
        client.disconnect();
        return;
      }

      // Add user to connected users
      this.notificationsService.addConnectedUser(
        user.id,
        user.role as NotificationRole,
        client.id,
      );

      // Join user to their personal room
      await client.join(`user:${user.id}`);
      
      // Join user to role-based rooms
      await client.join(`role:${user.role}`);

      // Join user to specific rooms based on role
      if (user.role === 'patient') {
        await client.join('patients');
      } else if (user.role === 'doctor' || user.role === 'hospital') {
        await client.join('healthcare');
      } else if (user.role === 'insurance') {
        await client.join('insurance');
      } else if (user.role === 'admin') {
        await client.join('admin');
      }

      // Send connection confirmation
      client.emit('connected', {
        message: 'Successfully connected to notifications',
        userId: user.id,
        role: user.role,
        timestamp: new Date(),
      });

      this.logger.log(`Client connected: ${user.id} (${user.role}) - Socket: ${client.id}`);
    } catch (error) {
      this.logger.error('Connection error:', error);
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    this.notificationsService.removeConnectedUser(client.id);
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  // Message Handlers
  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket): void {
    this.notificationsService.updateUserActivity(client.id);
    client.emit('pong', { timestamp: new Date() });
  }

  @SubscribeMessage('join-room')
  @UseGuards(WsRolesGuard)
  async handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { room: string },
  ): Promise<void> {
    const user = client.data.user as User;
    
    // Validate room access based on user role
    if (this.canJoinRoom(user, data.room)) {
      await client.join(data.room);
      client.emit('room-joined', { room: data.room, timestamp: new Date() });
      this.logger.log(`User ${user.id} joined room: ${data.room}`);
    } else {
      client.emit('error', { message: 'Access denied to room', room: data.room });
    }
  }

  @SubscribeMessage('leave-room')
  async handleLeaveRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { room: string },
  ): Promise<void> {
    await client.leave(data.room);
    client.emit('room-left', { room: data.room, timestamp: new Date() });
  }

  @SubscribeMessage('send-notification')
  @UseGuards(WsRolesGuard)
  @WsRoles('admin', 'doctor', 'hospital')
  async handleSendNotification(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: SendNotificationDto,
  ): Promise<void> {
    try {
      const result = await this.notificationsService.sendNotificationToUser(data);
      
      if (result.success) {
        // Emit notification to target user
        this.server.to(`user:${data.userId}`).emit('notification', {
          id: result.notificationId,
          type: data.type,
          title: data.title,
          message: data.message,
          data: data.data,
          timestamp: new Date(),
        });
      }

      client.emit('notification-sent', result);
    } catch (error) {
      client.emit('error', { message: 'Failed to send notification', error: error.message });
    }
  }

  @SubscribeMessage('broadcast-notification')
  @UseGuards(WsRolesGuard)
  @WsRoles('admin', 'hospital')
  async handleBroadcastNotification(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: BroadcastNotificationDto,
  ): Promise<void> {
    try {
      const result = await this.notificationsService.broadcastNotificationToRole(data);
      
      if (result.success) {
        // Emit notification to target role room
        this.server.to(`role:${data.targetRole}`).emit('notification', {
          type: data.type,
          title: data.title,
          message: data.message,
          data: data.data,
          timestamp: new Date(),
        });
      }

      client.emit('broadcast-sent', result);
    } catch (error) {
      client.emit('error', { message: 'Failed to broadcast notification', error: error.message });
    }
  }

  @SubscribeMessage('get-notification-history')
  async handleGetNotificationHistory(
    @ConnectedSocket() client: Socket,
    @MessageBody() query: NotificationHistoryQueryDto,
  ): Promise<void> {
    try {
      const user = client.data.user as User;
      
      // Ensure user can only get their own notifications unless admin
      if (user.role !== 'admin' && query.userId && query.userId !== user.id) {
        client.emit('error', { message: 'Access denied to other user notifications' });
        return;
      }

      // Set userId to current user if not provided and not admin
      if (!query.userId && user.role !== 'admin') {
        query.userId = user.id;
      }

      const history = await this.notificationsService.getNotificationHistory(query);
      client.emit('notification-history', history);
    } catch (error) {
      client.emit('error', { message: 'Failed to get notification history', error: error.message });
    }
  }

  @SubscribeMessage('mark-notification-read')
  async handleMarkNotificationRead(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { notificationId: string },
  ): Promise<void> {
    try {
      const user = client.data.user as User;
      const success = await this.notificationsService.markNotificationAsRead(
        data.notificationId,
        user.id,
      );

      client.emit('notification-marked-read', {
        success,
        notificationId: data.notificationId,
      });
    } catch (error) {
      client.emit('error', { message: 'Failed to mark notification as read', error: error.message });
    }
  }

  // Public Methods for Service Integration
  async emitReminderDue(userId: string, reminderData: any): Promise<void> {
    this.server.to(`user:${userId}`).emit('notification', {
      type: NotificationType.REMINDER_DUE,
      title: 'Medication Reminder',
      message: `Time to take your ${reminderData.medicineName}`,
      data: reminderData,
      timestamp: new Date(),
    });
  }

  async emitAdherenceUpdate(userId: string, adherenceData: any): Promise<void> {
    this.server.to(`user:${userId}`).emit('notification', {
      type: NotificationType.ADHERENCE_UPDATED,
      title: 'Adherence Updated',
      message: 'Your medication adherence has been updated',
      data: adherenceData,
      timestamp: new Date(),
    });
  }

  async emitAchievementUnlocked(userId: string, achievementData: any): Promise<void> {
    this.server.to(`user:${userId}`).emit('notification', {
      type: NotificationType.ACHIEVEMENT_UNLOCKED,
      title: 'Achievement Unlocked!',
      message: `Congratulations! You've earned: ${achievementData.achievementName}`,
      data: achievementData,
      timestamp: new Date(),
    });
  }

  async emitCriticalAdherenceAlert(doctorId: string, patientData: any): Promise<void> {
    this.server.to(`user:${doctorId}`).emit('notification', {
      type: NotificationType.ADHERENCE_CRITICAL,
      title: 'Critical Adherence Alert',
      message: `Patient ${patientData.patientName} has critical adherence issues`,
      data: patientData,
      timestamp: new Date(),
    });

    // Also emit to healthcare room for hospital staff
    this.server.to('healthcare').emit('notification', {
      type: NotificationType.ADHERENCE_CRITICAL,
      title: 'Critical Adherence Alert',
      message: `Patient ${patientData.patientName} has critical adherence issues`,
      data: patientData,
      timestamp: new Date(),
    });
  }

  async emitDashboardUpdate(targetRole: NotificationRole, dashboardData: any): Promise<void> {
    this.server.to(`role:${targetRole}`).emit('dashboard-update', {
      type: NotificationType.DASHBOARD_UPDATE,
      data: dashboardData,
      timestamp: new Date(),
    });
  }

  // Event Listeners for Real-time Notifications
  @OnEvent('reminder.due')
  async handleReminderDue(payload: any): Promise<void> {
    // Send WebSocket notification to connected users
    await this.emitReminderDue(payload.userId, payload);

    // Send push notification if user is not connected
    const userSockets = this.notificationsService.getUserSockets(payload.userId);
    if (userSockets.length === 0) {
      await this.notificationsService.sendReminderPushNotification(payload.userId, payload);
    }
  }

  @OnEvent('reminder.sent')
  async handleReminderSent(payload: any): Promise<void> {
    this.server.to(`user:${payload.userId}`).emit('reminder-sent', {
      reminderId: payload.reminderId,
      medicineName: payload.medicineName,
      type: payload.type,
      timestamp: payload.timestamp,
    });
  }

  @OnEvent('reminder.failed')
  async handleReminderFailed(payload: any): Promise<void> {
    this.server.to(`user:${payload.userId}`).emit('reminder-failed', {
      reminderId: payload.reminderId,
      medicineName: payload.medicineName,
      type: payload.type,
      error: payload.error,
      timestamp: payload.timestamp,
    });
  }

  @OnEvent('adherence.updated')
  async handleAdherenceUpdated(payload: any): Promise<void> {
    await this.emitAdherenceUpdate(payload.userId, payload);
  }

  @OnEvent('achievement.unlocked')
  async handleAchievementUnlocked(payload: any): Promise<void> {
    await this.emitAchievementUnlocked(payload.userId, payload);
  }

  @OnEvent('adherence.critical')
  async handleCriticalAdherence(payload: any): Promise<void> {
    await this.emitCriticalAdherenceAlert(payload.doctorId, payload);
  }

  @OnEvent('dashboard.update')
  async handleDashboardUpdate(payload: any): Promise<void> {
    await this.emitDashboardUpdate(payload.targetRole, payload.data);
  }

  // Private Methods
  private canJoinRoom(user: User, room: string): boolean {
    // Define room access rules based on user roles
    const roleRoomAccess = {
      patient: ['patients', `user:${user.id}`],
      doctor: ['healthcare', 'doctors', `user:${user.id}`],
      hospital: ['healthcare', 'hospitals', `user:${user.id}`],
      insurance: ['insurance', `user:${user.id}`],
      admin: ['admin', 'patients', 'healthcare', 'insurance', `user:${user.id}`],
    };

    const allowedRooms = roleRoomAccess[user.role] || [];
    return allowedRooms.includes(room) || room.startsWith(`user:${user.id}`);
  }
}
