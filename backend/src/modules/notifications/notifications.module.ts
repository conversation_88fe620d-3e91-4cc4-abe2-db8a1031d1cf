import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { NotificationsService } from './notifications.service';
import { NotificationsGateway } from './notifications.gateway';
import { NotificationsController } from './notifications.controller';
import { SupabaseService } from '../../config/supabase.service';
import { FCMService } from '../../common/services/fcm.service';
import { WsAuthGuard, WsRolesGuard } from '../../common/guards/ws-auth.guard';

@Module({
  imports: [
    JwtModule.registerAsync({
      useFactory: () => ({
        secret: process.env.JWT_SECRET,
        signOptions: { expiresIn: '24h' },
      }),
    }),
  ],
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    NotificationsGateway,
    SupabaseService,
    FCMService,
    WsAuthGuard,
    WsRolesGuard,
  ],
  exports: [NotificationsService, NotificationsGateway, FCMService],
})
export class NotificationsModule {}
