import { Controller, Get, Post, Body, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { ExternalServicesService } from './external-services.service';

export class TestSMSDto {
  @ApiProperty({ example: '+919101004681', description: 'Phone number in E.164 format' })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty({ example: 'Test SMS from MedCare', description: 'SMS message content' })
  @IsString()
  @IsNotEmpty()
  message: string;
}

export class TestCallDto {
  @ApiProperty({ example: '+919101004681', description: 'Phone number in E.164 format' })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty({ example: 'Test call from MedCare', description: 'Call message content' })
  @IsString()
  @IsNotEmpty()
  message: string;
}

export class TestElevenLabsDto {
  @ApiProperty({ example: '+919101004681', description: 'Phone number in E.164 format' })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty({ example: 'John Doe', description: 'Patient name' })
  @IsString()
  @IsNotEmpty()
  patientName: string;

  @ApiProperty({ example: 'Aspirin', description: 'Medicine name' })
  @IsString()
  @IsNotEmpty()
  medicineName: string;

  @ApiProperty({ example: '100mg', description: 'Medicine dosage' })
  @IsString()
  @IsNotEmpty()
  dosage: string;
}

@ApiTags('external-services')
@Controller('external-services')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ExternalServicesController {
  constructor(private readonly externalServicesService: ExternalServicesService) {}

  @Get('status')
  @ApiOperation({ summary: 'Get status of all external services' })
  @ApiResponse({ status: 200, description: 'External services status retrieved successfully' })
  async getServicesStatus() {
    return this.externalServicesService.getServicesStatus();
  }

  @Get('elevenlabs/status')
  @ApiOperation({ summary: 'Get detailed ElevenLabs service status' })
  @ApiResponse({ status: 200, description: 'ElevenLabs status retrieved successfully' })
  async getElevenLabsStatus() {
    return this.externalServicesService.getElevenLabsDetailedStatus();
  }

  @Post('test/sms')
  @ApiOperation({ summary: 'Test SMS functionality' })
  @ApiResponse({ status: 200, description: 'SMS test completed' })
  async testSMS(@Body() testSMSDto: TestSMSDto, @Request() req: any) {
    // Allow admins and patients to test SMS services
    if (!['admin', 'patient'].includes(req.user.role)) {
      throw new Error('Only admins and patients can test SMS services');
    }

    return this.externalServicesService.testSMS(testSMSDto.phoneNumber, testSMSDto.message);
  }

  @Post('test/call')
  @ApiOperation({ summary: 'Test voice call functionality' })
  @ApiResponse({ status: 200, description: 'Call test completed' })
  async testCall(@Body() testCallDto: TestCallDto, @Request() req: any) {
    // Allow admins and patients to test call services
    if (!['admin', 'patient'].includes(req.user.role)) {
      throw new Error('Only admins and patients can test call services');
    }

    return this.externalServicesService.testCall(testCallDto.phoneNumber, testCallDto.message);
  }

  @Post('test/elevenlabs')
  @ApiOperation({ summary: 'Test ElevenLabs conversational AI' })
  @ApiResponse({ status: 200, description: 'ElevenLabs test completed' })
  async testElevenLabs(@Body() testElevenLabsDto: TestElevenLabsDto, @Request() req: any) {
    // Allow admins and patients to test ElevenLabs services
    if (!['admin', 'patient'].includes(req.user.role)) {
      throw new Error('Only admins and patients can test ElevenLabs services');
    }

    return this.externalServicesService.testElevenLabsCall(
      testElevenLabsDto.phoneNumber,
      testElevenLabsDto.patientName,
      testElevenLabsDto.medicineName,
      testElevenLabsDto.dosage
    );
  }

  @Get('aws/status')
  @ApiOperation({ summary: 'Get AWS services status' })
  @ApiResponse({ status: 200, description: 'AWS services status retrieved successfully' })
  async getAWSStatus() {
    return this.externalServicesService.getAWSStatus();
  }

  @Post('test/textract')
  @ApiOperation({ summary: 'Test AWS Textract functionality' })
  @ApiResponse({ status: 200, description: 'Textract test completed' })
  async testTextract(@Request() req: any) {
    // Only allow admins to test external services
    if (req.user.role !== 'admin') {
      throw new Error('Only admins can test external services');
    }
    
    return this.externalServicesService.testTextract();
  }
}
