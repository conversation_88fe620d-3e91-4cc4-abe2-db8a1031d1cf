import { Injectable, Logger } from '@nestjs/common';
import { TwilioService } from '../../common/services/twilio.service';
import { ElevenLabsService } from '../../common/services/elevenlabs.service';
import { AwsService } from '../../common/services/aws.service';

@Injectable()
export class ExternalServicesService {
  private readonly logger = new Logger(ExternalServicesService.name);

  constructor(
    private readonly twilioService: TwilioService,
    private readonly elevenLabsService: ElevenLabsService,
    private readonly awsService: AwsService,
  ) {}

  async getServicesStatus() {
    const twilioStatus = this.twilioService.getServiceStatus();
    const elevenLabsStatus = this.elevenLabsService.getServiceStatus();
    
    return {
      twilio: {
        ...twilioStatus,
        description: 'SMS and voice call reminders',
      },
      elevenlabs: {
        ...elevenLabsStatus,
        description: 'Conversational AI for interactive medication reminders',
      },
      aws: {
        s3: { enabled: true, description: 'File storage for prescription images' },
        textract: { enabled: true, description: 'Text extraction from prescription images' },
      },
      integration: {
        reminders: {
          sms: twilioStatus.enabled,
          calls: twilioStatus.enabled || elevenLabsStatus.enabled,
          conversational_ai: elevenLabsStatus.enabled && elevenLabsStatus.agentConfigured,
        },
      },
    };
  }

  async testSMS(phoneNumber: string, message: string) {
    this.logger.log(`Testing SMS to ${phoneNumber}`);
    
    if (!this.twilioService.isValidPhoneNumber(phoneNumber)) {
      return {
        success: false,
        error: 'Invalid phone number format. Please use E.164 format (e.g., +**********)',
      };
    }

    const result = await this.twilioService.sendSMS({
      to: phoneNumber,
      message: message || 'This is a test message from MedCare API',
    });

    return {
      service: 'twilio_sms',
      ...result,
      timestamp: new Date().toISOString(),
    };
  }

  async testCall(phoneNumber: string, message: string) {
    this.logger.log(`Testing voice call to ${phoneNumber}`);
    
    if (!this.twilioService.isValidPhoneNumber(phoneNumber)) {
      return {
        success: false,
        error: 'Invalid phone number format. Please use E.164 format (e.g., +**********)',
      };
    }

    const result = await this.twilioService.makeCall({
      to: phoneNumber,
      message: message || 'This is a test call from MedCare API. Thank you for testing our service.',
    });

    return {
      service: 'twilio_call',
      ...result,
      timestamp: new Date().toISOString(),
    };
  }

  async testElevenLabsCall(
    phoneNumber: string,
    patientName: string,
    medicineName: string,
    dosage: string
  ) {
    this.logger.log(`Testing ElevenLabs conversational call to ${phoneNumber}`);
    
    if (!this.twilioService.isValidPhoneNumber(phoneNumber)) {
      return {
        success: false,
        error: 'Invalid phone number format. Please use E.164 format (e.g., +**********)',
      };
    }

    const result = await this.elevenLabsService.makeConversationalCall({
      agentId: '', // Will use configured agent
      phoneNumber,
      patientName: patientName || 'Test Patient',
      medicineName: medicineName || 'Test Medicine',
      dosage: dosage || '1 tablet',
    });

    return {
      service: 'elevenlabs_conversational_ai',
      ...result,
      timestamp: new Date().toISOString(),
    };
  }

  async getAWSStatus() {
    return {
      s3: {
        enabled: true,
        configured: true,
        description: 'S3 file storage for prescription images',
      },
      textract: {
        enabled: true,
        configured: true,
        description: 'Text extraction from prescription documents',
      },
    };
  }

  async testTextract() {
    this.logger.log('Testing AWS Textract with sample data');
    
    try {
      // Create a simple test image buffer (1x1 pixel PNG)
      const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x57, 0x63, 0xF8, 0x0F, 0x00, 0x00,
        0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);

      const result = await this.awsService.extractTextFromPrescription(testImageBuffer);
      
      return {
        service: 'aws_textract',
        success: true,
        result: {
          extractedText: result.extractedText || 'No text found in test image',
          confidence: result.confidence,
          medicineInfo: result.medicineInfo,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        service: 'aws_textract',
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  // Utility method to test all services at once
  async testAllServices(phoneNumber?: string) {
    const results = {
      timestamp: new Date().toISOString(),
      services: {},
    };

    // Test Twilio SMS
    if (phoneNumber) {
      results.services['twilio_sms'] = await this.testSMS(phoneNumber, 'Test SMS from MedCare');
      results.services['twilio_call'] = await this.testCall(phoneNumber, 'Test call from MedCare');
      results.services['elevenlabs_call'] = await this.testElevenLabsCall(
        phoneNumber,
        'Test Patient',
        'Test Medicine',
        '1 tablet'
      );
    }

    // Test AWS Textract
    results.services['aws_textract'] = await this.testTextract();

    return results;
  }

  /**
   * Get detailed ElevenLabs service status for debugging
   */
  async getElevenLabsDetailedStatus() {
    const status = this.elevenLabsService.getServiceStatus();

    return {
      ...status,
      environmentVariables: {
        apiKeyConfigured: !!process.env.ELEVENLABS_API_KEY,
        voiceIdConfigured: !!process.env.ELEVENLABS_VOICE_ID,
        agentIdConfigured: !!process.env.ELEVENLABS_AGENT_ID,
        agentPhoneNumberIdConfigured: !!process.env.ELEVENLABS_AGENT_PHONE_NUMBER_ID,
      },
      configurationDetails: {
        apiKey: process.env.ELEVENLABS_API_KEY ? `${process.env.ELEVENLABS_API_KEY.substring(0, 10)}...` : 'Not configured',
        voiceId: process.env.ELEVENLABS_VOICE_ID || 'Not configured',
        agentId: process.env.ELEVENLABS_AGENT_ID || 'Not configured',
        agentPhoneNumberId: process.env.ELEVENLABS_AGENT_PHONE_NUMBER_ID || 'Not configured',
      },
      timestamp: new Date().toISOString(),
    };
  }
}
