import { Injectable, NotFoundException, BadRequestException, ForbiddenException, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SupabaseService } from '../../config/supabase.service';
import { TwilioService } from '../../common/services/twilio.service';
import { ElevenLabsService } from '../../common/services/elevenlabs.service';
import { User, Reminder, ReminderLog } from '../../common/types';
import { CreateReminderDto, UpdateReminderDto, ReminderQueryDto, ReminderLogDto, BulkReminderDto } from './dto';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class RemindersService {
  private readonly logger = new Logger(RemindersService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly twilioService: TwilioService,
    private readonly elevenLabsService: ElevenLabsService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(createReminderDto: CreateReminderDto, currentUser: User): Promise<Reminder> {
    // Only patients, doctors, hospitals, and admins can create reminders
    if (!['patient', 'doctor', 'hospital', 'admin'].includes(currentUser.role)) {
      throw new ForbiddenException('Only patients, doctors, hospitals, and admins can create reminders');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Verify patient exists and user has permission
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('id, assigned_doctor_id')
      .eq('id', createReminderDto.patient_id)
      .single();

    if (patientError || !patient) {
      throw new NotFoundException('Patient not found');
    }

    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== createReminderDto.patient_id) {
      throw new ForbiddenException('You can only create reminders for yourself');
    } else if (currentUser.role === 'doctor' && patient.assigned_doctor_id !== currentUser.id) {
      throw new ForbiddenException('You can only create reminders for your assigned patients');
    }

    // Verify medicine exists
    const { data: medicine, error: medicineError } = await supabase
      .from('medicines')
      .select('id, patient_id')
      .eq('id', createReminderDto.medicine_id)
      .single();

    if (medicineError || !medicine) {
      throw new NotFoundException('Medicine not found');
    }

    // Ensure medicine belongs to the patient
    if (medicine.patient_id !== createReminderDto.patient_id) {
      throw new BadRequestException('Medicine does not belong to the specified patient');
    }

    // Validate scheduled time is in the future
    const scheduledTime = new Date(createReminderDto.scheduled_time);
    if (scheduledTime <= new Date()) {
      throw new BadRequestException('Scheduled time must be in the future');
    }

    const { data, error } = await supabase
      .from('reminders')
      .insert({
        patient_id: createReminderDto.patient_id,
        medicine_id: createReminderDto.medicine_id,
        reminder_type: createReminderDto.reminder_type,
        scheduled_time: createReminderDto.scheduled_time,
        message: createReminderDto.message,
        is_recurring: createReminderDto.is_recurring || false,
        recurrence_pattern: createReminderDto.recurrence_pattern,
        recurrence_interval: createReminderDto.recurrence_interval || 1,
        recurrence_days: createReminderDto.recurrence_days,
        end_date: createReminderDto.end_date,
        status: 'pending',
        is_active: true,
      })
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to create reminder: ${error.message}`);
    }

    return data;
  }

  async findAll(query: ReminderQueryDto, currentUser: User): Promise<Reminder[]> {
    const supabase = this.supabaseService.getAdminClient();
    
    let dbQuery = supabase
      .from('reminders')
      .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email), assigned_doctor_id),
        medicine:medicines(id, name, dosage, frequency, instructions)
      `);

    // Apply role-based filtering
    if (currentUser.role === 'patient') {
      dbQuery = dbQuery.eq('patient_id', currentUser.id);
    } else if (currentUser.role === 'doctor') {
      // Doctors can see reminders for their assigned patients
      const { data: patientIds } = await supabase
        .from('patients')
        .select('id')
        .eq('assigned_doctor_id', currentUser.id);
      
      const ids = patientIds?.map(p => p.id) || [];
      if (ids.length > 0) {
        dbQuery = dbQuery.in('patient_id', ids);
      } else {
        return [];
      }
    }

    // Apply query filters
    if (query.patient_id) {
      if (currentUser.role === 'patient' && query.patient_id !== currentUser.id) {
        throw new ForbiddenException('You can only view your own reminders');
      }
      dbQuery = dbQuery.eq('patient_id', query.patient_id);
    }

    if (query.medicine_id) {
      dbQuery = dbQuery.eq('medicine_id', query.medicine_id);
    }

    if (query.status && query.status !== 'all') {
      dbQuery = dbQuery.eq('status', query.status);
    }

    if (query.reminder_type && query.reminder_type !== 'all') {
      dbQuery = dbQuery.eq('reminder_type', query.reminder_type);
    }

    if (query.date_from) {
      dbQuery = dbQuery.gte('scheduled_time', query.date_from);
    }

    if (query.date_to) {
      dbQuery = dbQuery.lte('scheduled_time', query.date_to);
    }

    if (query.is_active !== undefined) {
      dbQuery = dbQuery.eq('is_active', query.is_active);
    }

    const { data, error } = await dbQuery.order('scheduled_time', { ascending: true });

    if (error) {
      throw new BadRequestException(`Failed to fetch reminders: ${error.message}`);
    }

    return data || [];
  }

  async getTodaysReminders(currentUser: User): Promise<Reminder[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return this.findAll({
      date_from: today.toISOString(),
      date_to: tomorrow.toISOString(),
      is_active: true,
    }, currentUser);
  }

  async getUpcomingReminders(currentUser: User, hours: number = 24): Promise<Reminder[]> {
    const now = new Date();
    const futureTime = new Date(now.getTime() + hours * 60 * 60 * 1000);

    return this.findAll({
      date_from: now.toISOString(),
      date_to: futureTime.toISOString(),
      status: 'pending',
      is_active: true,
    }, currentUser);
  }

  async getOverdueReminders(currentUser: User): Promise<Reminder[]> {
    const now = new Date();

    return this.findAll({
      date_to: now.toISOString(),
      status: 'pending',
      is_active: true,
    }, currentUser);
  }

  async findOne(id: string, currentUser: User): Promise<Reminder> {
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('reminders')
      .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email), assigned_doctor_id),
        medicine:medicines(id, name, dosage, frequency, instructions)
      `)
      .eq('id', id)
      .single();

    if (error || !data) {
      throw new NotFoundException(`Reminder with ID ${id} not found`);
    }

    // Check permissions
    if (currentUser.role === 'patient' && data.patient_id !== currentUser.id) {
      throw new ForbiddenException('You can only view your own reminders');
    } else if (currentUser.role === 'doctor') {
      const hasAccess = data.patient?.assigned_doctor_id === currentUser.id;
      if (!hasAccess) {
        throw new ForbiddenException('You can only view reminders for your patients');
      }
    }

    return data;
  }

  async update(id: string, updateReminderDto: UpdateReminderDto, currentUser: User): Promise<Reminder> {
    // Only patients, doctors, hospitals, and admins can update reminders
    if (!['patient', 'doctor', 'hospital', 'admin'].includes(currentUser.role)) {
      throw new ForbiddenException('Only patients, doctors, hospitals, and admins can update reminders');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Get existing reminder with permission check
    const existingReminder = await this.findOne(id, currentUser);

    // Validate scheduled time if provided
    if (updateReminderDto.scheduled_time) {
      const scheduledTime = new Date(updateReminderDto.scheduled_time);
      if (scheduledTime <= new Date()) {
        throw new BadRequestException('Scheduled time must be in the future');
      }
    }

    const { data, error } = await supabase
      .from('reminders')
      .update({
        ...(updateReminderDto.reminder_type && { reminder_type: updateReminderDto.reminder_type }),
        ...(updateReminderDto.scheduled_time && { scheduled_time: updateReminderDto.scheduled_time }),
        ...(updateReminderDto.message !== undefined && { message: updateReminderDto.message }),
        ...(updateReminderDto.is_recurring !== undefined && { is_recurring: updateReminderDto.is_recurring }),
        ...(updateReminderDto.recurrence_pattern && { recurrence_pattern: updateReminderDto.recurrence_pattern }),
        ...(updateReminderDto.recurrence_interval && { recurrence_interval: updateReminderDto.recurrence_interval }),
        ...(updateReminderDto.recurrence_days && { recurrence_days: updateReminderDto.recurrence_days }),
        ...(updateReminderDto.end_date !== undefined && { end_date: updateReminderDto.end_date }),
        ...(updateReminderDto.is_active !== undefined && { is_active: updateReminderDto.is_active }),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to update reminder: ${error.message}`);
    }

    return data;
  }

  async remove(id: string, currentUser: User): Promise<void> {
    // Only patients, doctors, hospitals, and admins can delete reminders
    if (!['patient', 'doctor', 'hospital', 'admin'].includes(currentUser.role)) {
      throw new ForbiddenException('Only patients, doctors, hospitals, and admins can delete reminders');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Get existing reminder with permission check
    await this.findOne(id, currentUser);

    const { error } = await supabase
      .from('reminders')
      .delete()
      .eq('id', id);

    if (error) {
      throw new BadRequestException(`Failed to delete reminder: ${error.message}`);
    }
  }

  async createBulkReminders(bulkReminderDto: BulkReminderDto, currentUser: User): Promise<Reminder[]> {
    // Only doctors, hospitals, and admins can create bulk reminders
    if (!['doctor', 'hospital', 'admin'].includes(currentUser.role)) {
      throw new ForbiddenException('Only doctors, hospitals, and admins can create bulk reminders');
    }

    const supabase = this.supabaseService.getAdminClient();
    const reminders: Reminder[] = [];

    // Verify medicine exists
    const { data: medicine, error: medicineError } = await supabase
      .from('medicines')
      .select('id')
      .eq('id', bulkReminderDto.medicine_id)
      .single();

    if (medicineError || !medicine) {
      throw new NotFoundException('Medicine not found');
    }

    // Create reminders for each patient
    for (const patientId of bulkReminderDto.patient_ids) {
      try {
        // Create scheduled time for today at the specified time
        const today = new Date();
        const [hours, minutes] = bulkReminderDto.time.split(':');
        const scheduledTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 
          parseInt(hours), parseInt(minutes));

        // If the time has already passed today, schedule for tomorrow
        if (scheduledTime <= new Date()) {
          scheduledTime.setDate(scheduledTime.getDate() + 1);
        }

        const reminderData: CreateReminderDto = {
          patient_id: patientId,
          medicine_id: bulkReminderDto.medicine_id,
          reminder_type: bulkReminderDto.reminder_type,
          scheduled_time: scheduledTime.toISOString(),
          message: bulkReminderDto.message,
          is_recurring: bulkReminderDto.is_recurring,
          recurrence_pattern: bulkReminderDto.recurrence_pattern,
        };

        const reminder = await this.create(reminderData, currentUser);
        reminders.push(reminder);
      } catch (error) {
        this.logger.warn(`Failed to create reminder for patient ${patientId}:`, error);
      }
    }

    return reminders;
  }

  // Scheduled task to process pending reminders
  @Cron(CronExpression.EVERY_MINUTE)
  async processPendingReminders(): Promise<void> {
    this.logger.log('🔄 Processing pending reminders...');
    
    const supabase = this.supabaseService.getAdminClient();
    const now = new Date();
    this.logger.log(`🕐 Current time: ${now.toISOString()}`);

    // Get reminders that are due
    const { data: dueReminders, error } = await supabase
      .from('reminders')
      .select(`
        *,
        patient:patients(id, emergency_contact, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency)
      `)
      .eq('status', 'pending')
      .eq('is_active', true)
      .lte('scheduled_time', now.toISOString());

    if (error) {
      this.logger.error('Failed to fetch due reminders:', error);
      return;
    }

    // Also get all pending reminders for debugging
    const { data: allPendingReminders } = await supabase
      .from('reminders')
      .select('id, scheduled_time, status')
      .eq('status', 'pending')
      .eq('is_active', true);

    this.logger.log(`📋 All pending reminders: ${JSON.stringify(allPendingReminders?.map(r => ({ id: r.id, scheduled_time: r.scheduled_time })) || [])}`);

    if (!dueReminders || dueReminders.length === 0) {
      this.logger.log('📭 No due reminders found');
      return;
    }

    this.logger.log(`📬 Found ${dueReminders.length} due reminders to process`);

    // Process each reminder
    for (const reminder of dueReminders) {
      this.logger.log(`🔍 Processing reminder ID: ${reminder.id}, scheduled: ${reminder.scheduled_time}`);
      try {
        await this.processReminder(reminder);
      } catch (error) {
        this.logger.error(`Failed to process reminder ${reminder.id}:`, error);
      }
    }
  }

  private async processReminder(reminder: any): Promise<void> {
    const supabase = this.supabaseService.getAdminClient();

    try {
      this.logger.log(`Processing reminder ${reminder.id} for patient ${reminder.patient_id}`);

      // Extract patient and medicine information
      const patientName = reminder.patient?.users?.name || 'Patient';
      const patientEmail = reminder.patient?.users?.email || '';
      const medicineName = reminder.medicine?.name || 'your medication';
      const dosage = reminder.medicine?.dosage || '';

      // Emit event for real-time notification to patient about due reminder
      this.eventEmitter.emit('reminder.due', {
        userId: reminder.patient_id,
        reminderId: reminder.id,
        medicineId: reminder.medicine_id,
        medicineName,
        dosage,
        scheduledTime: new Date(reminder.scheduled_time),
      });

      // Get patient's phone number from the joined data
      const phoneNumber = reminder.patient?.emergency_contact;
      this.logger.log(`📱 Patient phone number: ${phoneNumber}`);
      let reminderSent = false;
      let errorMessage = '';

      // Send reminder based on type
      if (reminder.reminder_type === 'sms' && phoneNumber) {
        this.logger.log(`📤 Attempting to send SMS to ${phoneNumber}`);
        try {
          const smsResult = await this.twilioService.sendMedicationReminder(
            phoneNumber,
            patientName,
            medicineName,
            dosage,
            'sms'
          );
          this.logger.log(`📤 SMS result: ${JSON.stringify(smsResult)}`);
          reminderSent = smsResult.success;
          if (!smsResult.success) {
            errorMessage = smsResult.error || 'SMS failed';
            this.logger.error(`❌ SMS failed: ${errorMessage}`);
          } else {
            this.logger.log(`✅ SMS sent successfully`);
          }
        } catch (error) {
          this.logger.error(`💥 SMS exception: ${error.message}`);
          errorMessage = `SMS exception: ${error.message}`;
        }
      } else if (reminder.reminder_type === 'call' && phoneNumber) {
        // Try ElevenLabs conversational AI first, fallback to Twilio
        const elevenLabsStatus = this.elevenLabsService.getServiceStatus();

        if (elevenLabsStatus.enabled && elevenLabsStatus.agentConfigured) {
          const callResult = await this.elevenLabsService.makeConversationalCall({
            phoneNumber,
            patientName,
            medicineName,
            dosage,
            // Pass metadata for webhook processing
            patientId: reminder.patient_id,
            medicineId: reminder.medicine_id,
            reminderId: reminder.id,
          });
          reminderSent = callResult.success;
          if (!callResult.success) {
            errorMessage = callResult.error || 'ElevenLabs call failed';
          }
        } else {
          // Fallback to Twilio call
          const callResult = await this.twilioService.sendMedicationReminder(
            phoneNumber,
            patientName,
            medicineName,
            dosage,
            'call'
          );
          reminderSent = callResult.success;
          if (!callResult.success) {
            errorMessage = callResult.error || 'Twilio call failed';
          }
        }
      } else if (reminder.reminder_type === 'email' && patientEmail) {
        // Email reminders would be handled here (not implemented in this phase)
        this.logger.log(`Email reminder not implemented yet for ${patientEmail}`);
        reminderSent = false;
        errorMessage = 'Email reminders not implemented';
      } else {
        errorMessage = 'No valid contact method available';
      }

      // Update reminder status based on result
      const status = reminderSent ? 'sent' : 'failed';
      await supabase
        .from('reminders')
        .update({ status })
        .eq('id', reminder.id);

      // Create reminder log
      await supabase
        .from('reminder_logs')
        .insert({
          reminder_id: reminder.id,
          status,
          message: reminderSent
            ? `Reminder sent successfully via ${reminder.reminder_type}`
            : `Failed to send reminder: ${errorMessage}`,
        });

      // Emit event for real-time notification about reminder status
      if (reminderSent) {
        this.eventEmitter.emit('reminder.sent', {
          userId: reminder.patient_id,
          reminderId: reminder.id,
          medicineName,
          type: reminder.reminder_type,
          timestamp: new Date(),
        });
      } else {
        this.eventEmitter.emit('reminder.failed', {
          userId: reminder.patient_id,
          reminderId: reminder.id,
          medicineName,
          type: reminder.reminder_type,
          error: errorMessage,
          timestamp: new Date(),
        });
      }

      // If recurring and successfully sent, create next reminder
      if (reminder.is_recurring && reminderSent) {
        await this.createNextRecurringReminder(reminder);
      }

    } catch (error) {
      this.logger.error(`Failed to process reminder ${reminder.id}:`, error);

      // Mark as failed
      await supabase
        .from('reminders')
        .update({ status: 'failed' })
        .eq('id', reminder.id);

      // Log the failure
      await supabase
        .from('reminder_logs')
        .insert({
          reminder_id: reminder.id,
          status: 'failed',
          error_message: error.message,
        });

      throw error;
    }
  }

  private async createNextRecurringReminder(reminder: any): Promise<void> {
    const supabase = this.supabaseService.getAdminClient();
    
    const currentTime = new Date(reminder.scheduled_time);
    let nextTime: Date;

    switch (reminder.recurrence_pattern) {
      case 'daily':
        nextTime = new Date(currentTime.getTime() + (reminder.recurrence_interval * 24 * 60 * 60 * 1000));
        break;
      case 'weekly':
        nextTime = new Date(currentTime.getTime() + (reminder.recurrence_interval * 7 * 24 * 60 * 60 * 1000));
        break;
      case 'monthly':
        nextTime = new Date(currentTime);
        nextTime.setMonth(nextTime.getMonth() + reminder.recurrence_interval);
        break;
      default:
        return;
    }

    // Check if we've reached the end date
    if (reminder.end_date && nextTime > new Date(reminder.end_date)) {
      return;
    }

    // Create the next reminder
    await supabase
      .from('reminders')
      .insert({
        patient_id: reminder.patient_id,
        medicine_id: reminder.medicine_id,
        reminder_type: reminder.reminder_type,
        scheduled_time: nextTime.toISOString(),
        message: reminder.message,
        is_recurring: reminder.is_recurring,
        recurrence_pattern: reminder.recurrence_pattern,
        recurrence_interval: reminder.recurrence_interval,
        recurrence_days: reminder.recurrence_days,
        end_date: reminder.end_date,
        status: 'pending',
        is_active: true,
      });
  }

  /**
   * Get reminder settings for a patient
   */
  async getReminderSettings(currentUser: User): Promise<{
    pushNotifications: boolean;
    smsReminders: boolean;
    voiceCalls: boolean;
    emailReminders: boolean;
    reminderTiming: number;
    snoozeInterval: number;
    escalationEnabled: boolean;
    preferredReminderType: string;
  }> {
    if (currentUser.role !== 'patient') {
      throw new ForbiddenException('Only patients can access reminder settings');
    }

    const supabase = this.supabaseService.getAdminClient();

    const { data: patient, error } = await supabase
      .from('patients')
      .select(`
        push_notifications,
        sms_reminders,
        voice_calls,
        email_reminders,
        reminder_timing,
        snooze_interval,
        escalation_enabled,
        preferred_reminder_type
      `)
      .eq('id', currentUser.id)
      .single();

    if (error || !patient) {
      throw new NotFoundException('Patient settings not found');
    }

    return {
      pushNotifications: patient.push_notifications ?? true,
      smsReminders: patient.sms_reminders ?? true,
      voiceCalls: patient.voice_calls ?? false,
      emailReminders: patient.email_reminders ?? true,
      reminderTiming: patient.reminder_timing ?? 15,
      snoozeInterval: patient.snooze_interval ?? 15,
      escalationEnabled: patient.escalation_enabled ?? true,
      preferredReminderType: patient.preferred_reminder_type ?? 'sms',
    };
  }

  /**
   * Update reminder settings for a patient
   */
  async updateReminderSettings(
    currentUser: User,
    settings: {
      pushNotifications?: boolean;
      smsReminders?: boolean;
      voiceCalls?: boolean;
      emailReminders?: boolean;
      reminderTiming?: number;
      snoozeInterval?: number;
      escalationEnabled?: boolean;
      preferredReminderType?: 'sms' | 'call' | 'notification';
    }
  ): Promise<{ message: string }> {
    if (currentUser.role !== 'patient') {
      throw new ForbiddenException('Only patients can update reminder settings');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Build update object with snake_case field names
    const updateData: any = {};
    if (settings.pushNotifications !== undefined) updateData.push_notifications = settings.pushNotifications;
    if (settings.smsReminders !== undefined) updateData.sms_reminders = settings.smsReminders;
    if (settings.voiceCalls !== undefined) updateData.voice_calls = settings.voiceCalls;
    if (settings.emailReminders !== undefined) updateData.email_reminders = settings.emailReminders;
    if (settings.reminderTiming !== undefined) updateData.reminder_timing = settings.reminderTiming;
    if (settings.snoozeInterval !== undefined) updateData.snooze_interval = settings.snoozeInterval;
    if (settings.escalationEnabled !== undefined) updateData.escalation_enabled = settings.escalationEnabled;
    if (settings.preferredReminderType !== undefined) updateData.preferred_reminder_type = settings.preferredReminderType;

    updateData.updated_at = new Date().toISOString();

    const { error } = await supabase
      .from('patients')
      .update(updateData)
      .eq('id', currentUser.id);

    if (error) {
      this.logger.error('Failed to update reminder settings:', error);
      throw new BadRequestException('Failed to update reminder settings');
    }

    this.logger.log(`Reminder settings updated for patient ${currentUser.id}`);
    return { message: 'Reminder settings updated successfully' };
  }
}
