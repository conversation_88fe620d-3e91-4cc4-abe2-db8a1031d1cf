import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { RemindersService } from './reminders.service';
import { CreateReminderDto, UpdateReminderDto, ReminderQueryDto, BulkReminderDto } from './dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../../common/types';

@ApiTags('reminders')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('reminders')
export class RemindersController {
  constructor(private readonly remindersService: RemindersService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new reminder' })
  @ApiResponse({ status: 201, description: 'Reminder created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async create(
    @Body() createReminderDto: CreateReminderDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.remindersService.create(createReminderDto, currentUser);
  }

  @Post('bulk')
  @ApiOperation({ summary: 'Create bulk reminders for multiple patients' })
  @ApiResponse({ status: 201, description: 'Bulk reminders created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('doctor', 'hospital', 'admin')
  async createBulk(
    @Body() bulkReminderDto: BulkReminderDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.remindersService.createBulkReminders(bulkReminderDto, currentUser);
  }

  @Get()
  @ApiOperation({ summary: 'Get all reminders' })
  @ApiQuery({ name: 'patient_id', required: false, description: 'Filter by patient ID' })
  @ApiQuery({ name: 'medicine_id', required: false, description: 'Filter by medicine ID' })
  @ApiQuery({ name: 'status', required: false, enum: ['pending', 'sent', 'failed', 'all'], description: 'Filter by status' })
  @ApiQuery({ name: 'reminder_type', required: false, enum: ['sms', 'call', 'both', 'all'], description: 'Filter by reminder type' })
  @ApiQuery({ name: 'date_from', required: false, description: 'Filter from date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'date_to', required: false, description: 'Filter to date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'is_active', required: false, description: 'Filter by active status' })
  @ApiResponse({ status: 200, description: 'Reminders retrieved successfully' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findAll(
    @Query() query: ReminderQueryDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.remindersService.findAll(query, currentUser);
  }

  @Get('patient/:patientId')
  @ApiOperation({ summary: 'Get reminders for a specific patient' })
  @ApiResponse({ status: 200, description: 'Patient reminders retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Patient not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findByPatient(
    @Param('patientId', ParseUUIDPipe) patientId: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.remindersService.findAll({ patient_id: patientId }, currentUser);
  }

  @Get('medicine/:medicineId')
  @ApiOperation({ summary: 'Get reminders for a specific medicine' })
  @ApiResponse({ status: 200, description: 'Medicine reminders retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Medicine not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findByMedicine(
    @Param('medicineId', ParseUUIDPipe) medicineId: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.remindersService.findAll({ medicine_id: medicineId }, currentUser);
  }

  @Get('today')
  @ApiOperation({ summary: 'Get today\'s reminders' })
  @ApiResponse({ status: 200, description: 'Today\'s reminders retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async getTodaysReminders(@CurrentUser() currentUser: User) {
    return this.remindersService.getTodaysReminders(currentUser);
  }

  @Get('upcoming')
  @ApiOperation({ summary: 'Get upcoming reminders' })
  @ApiQuery({ name: 'hours', required: false, description: 'Number of hours to look ahead (default: 24)' })
  @ApiResponse({ status: 200, description: 'Upcoming reminders retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async getUpcomingReminders(
    @Query('hours') hours: number = 24,
    @CurrentUser() currentUser: User,
  ) {
    return this.remindersService.getUpcomingReminders(currentUser, hours);
  }

  @Get('overdue')
  @ApiOperation({ summary: 'Get overdue reminders' })
  @ApiResponse({ status: 200, description: 'Overdue reminders retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async getOverdueReminders(@CurrentUser() currentUser: User) {
    return this.remindersService.getOverdueReminders(currentUser);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get reminder by ID' })
  @ApiResponse({ status: 200, description: 'Reminder retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Reminder not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.remindersService.findOne(id, currentUser);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update reminder' })
  @ApiResponse({ status: 200, description: 'Reminder updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Reminder not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateReminderDto: UpdateReminderDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.remindersService.update(id, updateReminderDto, currentUser);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete reminder' })
  @ApiResponse({ status: 200, description: 'Reminder deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Reminder not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    await this.remindersService.remove(id, currentUser);
    return { message: 'Reminder deleted successfully' };
  }

  @Get('settings')
  @ApiOperation({ summary: 'Get reminder settings for current user' })
  @ApiResponse({ status: 200, description: 'Reminder settings retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient')
  async getReminderSettings(@CurrentUser() currentUser: User) {
    return this.remindersService.getReminderSettings(currentUser);
  }

  @Patch('settings')
  @ApiOperation({ summary: 'Update reminder settings for current user' })
  @ApiResponse({ status: 200, description: 'Reminder settings updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient')
  async updateReminderSettings(
    @Body() settings: {
      pushNotifications?: boolean;
      smsReminders?: boolean;
      voiceCalls?: boolean;
      emailReminders?: boolean;
      reminderTiming?: number;
      snoozeInterval?: number;
      escalationEnabled?: boolean;
      preferredReminderType?: 'sms' | 'call' | 'notification';
    },
    @CurrentUser() currentUser: User,
  ) {
    return this.remindersService.updateReminderSettings(currentUser, settings);
  }
}
