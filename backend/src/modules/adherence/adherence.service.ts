import { Injectable, NotFoundException, BadRequestException, ForbiddenException, Inject, forwardRef } from '@nestjs/common';
import { SupabaseService } from '../../config/supabase.service';
import { User, AdherenceRecord } from '../../common/types';
import {
  CreateAdherenceRecordDto,
  UpdateAdherenceRecordDto,
  AdherenceQueryDto,
  AdherenceAnalyticsDto,
  AdherenceAnalytics,
  AdherenceStatus
} from './dto';

@Injectable()
export class AdherenceService {
  constructor(private readonly supabaseService: SupabaseService) {}

  async create(createAdherenceDto: CreateAdherenceRecordDto, currentUser: User): Promise<AdherenceRecord> {
    // Validate permissions
    if (currentUser.role === 'patient' && currentUser.id !== createAdherenceDto.patient_id) {
      throw new ForbiddenException('Patients can only record their own adherence');
    }

    const supabase = this.supabaseService.getClient();

    // Verify patient exists
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('id')
      .eq('id', createAdherenceDto.patient_id)
      .single();

    if (patientError || !patient) {
      throw new NotFoundException(`Patient with ID ${createAdherenceDto.patient_id} not found`);
    }

    // Verify medicine exists
    const { data: medicine, error: medicineError } = await supabase
      .from('medicines')
      .select('id, name')
      .eq('id', createAdherenceDto.medicine_id)
      .single();

    if (medicineError || !medicine) {
      throw new NotFoundException(`Medicine with ID ${createAdherenceDto.medicine_id} not found`);
    }

    // Check for duplicate adherence record
    const { data: existing, error: existingError } = await supabase
      .from('adherence_records')
      .select('id')
      .eq('patient_id', createAdherenceDto.patient_id)
      .eq('medicine_id', createAdherenceDto.medicine_id)
      .eq('scheduled_time', createAdherenceDto.scheduled_time)
      .single();

    if (existing) {
      throw new BadRequestException('Adherence record already exists for this medication and time');
    }

    // Create adherence record
    const { data, error } = await supabase
      .from('adherence_records')
      .insert({
        patient_id: createAdherenceDto.patient_id,
        medicine_id: createAdherenceDto.medicine_id,
        scheduled_time: createAdherenceDto.scheduled_time,
        taken_time: createAdherenceDto.taken_time || null,
        status: createAdherenceDto.status,
        notes: createAdherenceDto.notes || null,
      })
      .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency)
      `)
      .single();

    if (error) {
      throw new BadRequestException(`Failed to create adherence record: ${error.message}`);
    }

    // Update gamification stats and check achievements
    await this.updateGamificationAfterAdherence(data);

    return data;
  }

  private async updateGamificationAfterAdherence(adherenceRecord: AdherenceRecord): Promise<void> {
    try {
      // Import services dynamically to avoid circular dependencies
      const { GamificationService } = await import('../gamification/gamification.service');
      const { AchievementsService } = await import('../achievements/achievements.service');

      const gamificationService = new GamificationService(this.supabaseService);
      const achievementsService = new AchievementsService(this.supabaseService);

      // Update gamification stats
      await gamificationService.updateStatsAfterAdherence(
        adherenceRecord.patient_id,
        adherenceRecord.status,
        new Date(adherenceRecord.scheduled_time),
        adherenceRecord.taken_time ? new Date(adherenceRecord.taken_time) : undefined
      );

      // Check and award achievements
      await achievementsService.checkAndAwardAchievements(adherenceRecord.patient_id);
    } catch (error) {
      // Log error but don't fail the adherence record creation
      console.error('Failed to update gamification after adherence:', error);
    }
  }

  async findAll(query: AdherenceQueryDto, currentUser: User): Promise<AdherenceRecord[]> {
    const supabase = this.supabaseService.getAdminClient();

    let dbQuery = supabase
      .from('adherence_records')
      .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency, instructions)
      `);

    // Apply role-based filtering
    if (currentUser.role === 'patient') {
      dbQuery = dbQuery.eq('patient_id', currentUser.id);
    } else if (currentUser.role === 'doctor') {
      // Doctors can see adherence for their assigned patients
      const { data: assignedPatients } = await supabase
        .from('patients')
        .select('id')
        .eq('assigned_doctor_id', currentUser.id);
      
      if (assignedPatients && assignedPatients.length > 0) {
        const patientIds = assignedPatients.map(p => p.id);
        dbQuery = dbQuery.in('patient_id', patientIds);
      } else {
        return []; // Doctor has no assigned patients
      }
    }

    // Apply filters
    if (query.patient_id) {
      dbQuery = dbQuery.eq('patient_id', query.patient_id);
    }

    if (query.medicine_id) {
      dbQuery = dbQuery.eq('medicine_id', query.medicine_id);
    }

    if (query.status) {
      dbQuery = dbQuery.eq('status', query.status);
    }

    if (query.start_date) {
      dbQuery = dbQuery.gte('scheduled_time', query.start_date);
    }

    if (query.end_date) {
      dbQuery = dbQuery.lte('scheduled_time', query.end_date);
    }

    // Apply pagination
    dbQuery = dbQuery
      .order('scheduled_time', { ascending: false })
      .range(query.offset || 0, (query.offset || 0) + (query.limit || 50) - 1);

    const { data, error } = await dbQuery;

    if (error) {
      throw new BadRequestException(`Failed to fetch adherence records: ${error.message}`);
    }

    return data || [];
  }

  async findOne(id: string, currentUser: User): Promise<AdherenceRecord> {
    const supabase = this.supabaseService.getAdminClient();

    const { data, error } = await supabase
      .from('adherence_records')
      .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency, instructions, side_effects)
      `)
      .eq('id', id)
      .single();

    if (error || !data) {
      throw new NotFoundException(`Adherence record with ID ${id} not found`);
    }

    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== data.patient_id) {
      throw new ForbiddenException('Patients can only view their own adherence records');
    }

    if (currentUser.role === 'doctor') {
      // Verify patient is assigned to this doctor
      const { data: patient } = await supabase
        .from('patients')
        .select('assigned_doctor_id')
        .eq('id', data.patient_id)
        .single();

      if (!patient || patient.assigned_doctor_id !== currentUser.id) {
        throw new ForbiddenException('You can only view adherence records for your assigned patients');
      }
    }

    return data;
  }

  async update(id: string, updateAdherenceDto: UpdateAdherenceRecordDto, currentUser: User): Promise<AdherenceRecord> {
    // First get the existing record to check permissions
    const existingRecord = await this.findOne(id, currentUser);

    const supabase = this.supabaseService.getAdminClient();

    const { data, error } = await supabase
      .from('adherence_records')
      .update({
        taken_time: updateAdherenceDto.taken_time || existingRecord.taken_time,
        status: updateAdherenceDto.status || existingRecord.status,
        notes: updateAdherenceDto.notes !== undefined ? updateAdherenceDto.notes : existingRecord.notes,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency)
      `)
      .single();

    if (error) {
      throw new BadRequestException(`Failed to update adherence record: ${error.message}`);
    }

    return data;
  }

  async remove(id: string, currentUser: User): Promise<void> {
    // First get the existing record to check permissions
    await this.findOne(id, currentUser);

    const supabase = this.supabaseService.getClient();

    const { error } = await supabase
      .from('adherence_records')
      .delete()
      .eq('id', id);

    if (error) {
      throw new BadRequestException(`Failed to delete adherence record: ${error.message}`);
    }
  }

  async getAnalytics(analyticsDto: AdherenceAnalyticsDto, currentUser: User): Promise<AdherenceAnalytics> {
    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== analyticsDto.patient_id) {
      throw new ForbiddenException('Patients can only view their own analytics');
    }

    if (currentUser.role === 'doctor') {
      // Verify patient is assigned to this doctor
      const supabase = this.supabaseService.getClient();
      const { data: patient } = await supabase
        .from('patients')
        .select('assigned_doctor_id')
        .eq('id', analyticsDto.patient_id)
        .single();

      if (!patient || patient.assigned_doctor_id !== currentUser.id) {
        throw new ForbiddenException('You can only view analytics for your assigned patients');
      }
    }

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - (analyticsDto.days || 30));

    const supabase = this.supabaseService.getAdminClient();

    // Get adherence records for the period
    const { data: records, error } = await supabase
      .from('adherence_records')
      .select(`
        *,
        medicine:medicines(id, name)
      `)
      .eq('patient_id', analyticsDto.patient_id)
      .gte('scheduled_time', startDate.toISOString())
      .lte('scheduled_time', endDate.toISOString())
      .order('scheduled_time', { ascending: true });

    if (error) {
      throw new BadRequestException(`Failed to fetch analytics data: ${error.message}`);
    }

    const adherenceRecords = records || [];

    // Calculate basic stats
    const totalScheduled = adherenceRecords.length;
    const totalTaken = adherenceRecords.filter(r => r.status === 'taken').length;
    const totalMissed = adherenceRecords.filter(r => r.status === 'missed').length;
    const totalSkipped = adherenceRecords.filter(r => r.status === 'skipped').length;
    const adherenceRate = totalScheduled > 0 ? (totalTaken / totalScheduled) * 100 : 0;

    // Calculate on-time and late rates
    const onTimeRecords = adherenceRecords.filter(r => {
      if (r.status !== 'taken' || !r.taken_time) return false;
      const scheduled = new Date(r.scheduled_time);
      const taken = new Date(r.taken_time);
      const diffHours = (taken.getTime() - scheduled.getTime()) / (1000 * 60 * 60);
      return diffHours <= 1; // Within 1 hour is considered on time
    });

    const lateRecords = adherenceRecords.filter(r => {
      if (r.status !== 'taken' || !r.taken_time) return false;
      const scheduled = new Date(r.scheduled_time);
      const taken = new Date(r.taken_time);
      const diffHours = (taken.getTime() - scheduled.getTime()) / (1000 * 60 * 60);
      return diffHours > 1; // More than 1 hour is considered late
    });

    const onTimeRate = totalTaken > 0 ? (onTimeRecords.length / totalTaken) * 100 : 0;
    const lateRate = totalTaken > 0 ? (lateRecords.length / totalTaken) * 100 : 0;

    // Calculate streaks
    const { currentStreak, longestStreak } = this.calculateStreaks(adherenceRecords);

    // Calculate daily breakdown
    const dailyBreakdown = this.calculateDailyBreakdown(adherenceRecords, startDate, endDate);

    // Calculate medicine breakdown
    const medicineBreakdown = this.calculateMedicineBreakdown(adherenceRecords);

    return {
      patient_id: analyticsDto.patient_id,
      total_scheduled: totalScheduled,
      total_taken: totalTaken,
      total_missed: totalMissed,
      total_skipped: totalSkipped,
      adherence_rate: Math.round(adherenceRate * 100) / 100,
      on_time_rate: Math.round(onTimeRate * 100) / 100,
      late_rate: Math.round(lateRate * 100) / 100,
      current_streak: currentStreak,
      longest_streak: longestStreak,
      daily_breakdown: dailyBreakdown,
      medicine_breakdown: medicineBreakdown,
    };
  }

  private calculateStreaks(records: any[]): { currentStreak: number; longestStreak: number } {
    if (records.length === 0) return { currentStreak: 0, longestStreak: 0 };

    // Group records by date
    const dailyRecords = new Map<string, any[]>();
    records.forEach(record => {
      const date = new Date(record.scheduled_time).toDateString();
      if (!dailyRecords.has(date)) {
        dailyRecords.set(date, []);
      }
      dailyRecords.get(date)!.push(record);
    });

    // Calculate daily adherence rates
    const dailyRates: { date: string; rate: number }[] = [];
    for (const [date, dayRecords] of dailyRecords) {
      const taken = dayRecords.filter(r => r.status === 'taken').length;
      const total = dayRecords.length;
      const rate = total > 0 ? (taken / total) * 100 : 0;
      dailyRates.push({ date, rate });
    }

    // Sort by date
    dailyRates.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Calculate streaks (>80% adherence required)
    let currentStreak = 0;
    let longestStreak = 0;
    let tempStreak = 0;

    for (let i = dailyRates.length - 1; i >= 0; i--) {
      if (dailyRates[i].rate >= 80) {
        tempStreak++;
        if (i === dailyRates.length - 1) {
          currentStreak = tempStreak;
        }
      } else {
        if (i === dailyRates.length - 1) {
          currentStreak = 0;
        }
        longestStreak = Math.max(longestStreak, tempStreak);
        tempStreak = 0;
      }
    }

    longestStreak = Math.max(longestStreak, tempStreak);

    return { currentStreak, longestStreak };
  }

  private calculateDailyBreakdown(records: any[], startDate: Date, endDate: Date): any[] {
    const dailyMap = new Map<string, { scheduled: number; taken: number; missed: number; skipped: number }>();

    // Initialize all dates in range
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split('T')[0];
      dailyMap.set(dateStr, { scheduled: 0, taken: 0, missed: 0, skipped: 0 });
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Populate with actual data
    records.forEach(record => {
      const dateStr = new Date(record.scheduled_time).toISOString().split('T')[0];
      const dayData = dailyMap.get(dateStr);
      if (dayData) {
        dayData.scheduled++;
        if (record.status === 'taken') dayData.taken++;
        else if (record.status === 'missed') dayData.missed++;
        else if (record.status === 'skipped') dayData.skipped++;
      }
    });

    // Convert to array with rates
    return Array.from(dailyMap.entries()).map(([date, data]) => ({
      date,
      ...data,
      rate: data.scheduled > 0 ? Math.round((data.taken / data.scheduled) * 10000) / 100 : 0,
    }));
  }

  private calculateMedicineBreakdown(records: any[]): any[] {
    const medicineMap = new Map<string, {
      medicine_id: string;
      medicine_name: string;
      scheduled: number;
      taken: number;
      missed: number;
      skipped: number;
    }>();

    records.forEach(record => {
      const medicineId = record.medicine_id;
      const medicineName = record.medicine?.name || 'Unknown Medicine';

      if (!medicineMap.has(medicineId)) {
        medicineMap.set(medicineId, {
          medicine_id: medicineId,
          medicine_name: medicineName,
          scheduled: 0,
          taken: 0,
          missed: 0,
          skipped: 0,
        });
      }

      const medicineData = medicineMap.get(medicineId)!;
      medicineData.scheduled++;
      if (record.status === 'taken') medicineData.taken++;
      else if (record.status === 'missed') medicineData.missed++;
      else if (record.status === 'skipped') medicineData.skipped++;
    });

    // Convert to array with rates
    return Array.from(medicineMap.values()).map(data => ({
      ...data,
      rate: data.scheduled > 0 ? Math.round((data.taken / data.scheduled) * 10000) / 100 : 0,
    }));
  }
}
