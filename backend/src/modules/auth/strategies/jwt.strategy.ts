import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';

import { AuthService } from '../auth.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private authService: AuthService,
    private configService: ConfigService,
  ) {
    const jwtSecret = configService.get<string>('jwt.secret') || 'fallback-secret';
    console.log('🔧 JWT Strategy initialized with secret:', jwtSecret.substring(0, 10) + '...');

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtSecret,
    });
  }

  async validate(payload: any) {
    console.log('🔐 JWT Strategy validate called with payload:', {
      sub: payload.sub,
      email: payload.email,
      role: payload.role,
      iat: payload.iat,
      exp: payload.exp
    });

    try {
      const user = await this.authService.validateUser(payload.sub);
      if (!user) {
        console.error('❌ JWT validation failed: User not found for ID:', payload.sub);
        throw new UnauthorizedException('User not found');
      }

      console.log('✅ JWT validation successful for user:', {
        id: user.id,
        email: user.email,
        role: user.role
      });

      return user;
    } catch (error) {
      console.error('❌ JWT validation error:', error.message);
      throw new UnauthorizedException('Token validation failed');
    }
  }
}
