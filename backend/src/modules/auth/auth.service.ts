import { Injectable, UnauthorizedException, ConflictException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';

import { SupabaseService } from '../../config/supabase.service';
import { LoginDto, RegisterDto, AuthResponseDto, GoogleSignupDto } from './dto';
import { UserRole } from '../../common/types';

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    private supabaseService: SupabaseService,
    private configService: ConfigService,
  ) {}

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    const { email, password, name, role, ...roleSpecificData } = registerDto;

    // Check if user already exists
    const { data: existingUser } = await this.supabaseService
      .getAdminClient()
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user in Supabase Auth
    const { data: authUser, error: authError } = await this.supabaseService
      .getAdminClient()
      .auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          name,
          role,
        },
      });

    if (authError) {
      throw new BadRequestException(authError.message);
    }

    // Create user in our users table
    const { data: user, error: userError } = await this.supabaseService
      .getAdminClient()
      .from('users')
      .insert({
        id: authUser.user.id,
        email,
        name,
        role,
      })
      .select()
      .single();

    if (userError) {
      // Cleanup auth user if database insert fails
      await this.supabaseService.getAdminClient().auth.admin.deleteUser(authUser.user.id);
      throw new BadRequestException(userError.message);
    }

    // Create role-specific record
    await this.createRoleSpecificRecord(user.id, role, roleSpecificData);

    // Generate JWT token
    const payload = { sub: user.id, email: user.email, role: user.role };
    const accessToken = this.jwtService.sign(payload);

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        avatar: user.avatar,
      },
      accessToken,
    };
  }

  async login(loginDto: LoginDto): Promise<AuthResponseDto> {
    const { email, password } = loginDto;

    // Authenticate with Supabase
    const { data: authData, error: authError } = await this.supabaseService
      .getClient()
      .auth.signInWithPassword({
        email,
        password,
      });

    if (authError) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Get user details from our database
    const { data: user, error: userError } = await this.supabaseService
      .getAdminClient()
      .from('users')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (userError || !user) {
      throw new UnauthorizedException('User not found');
    }

    // Generate JWT token
    const payload = { sub: user.id, email: user.email, role: user.role };
    const accessToken = this.jwtService.sign(payload);

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        avatar: user.avatar,
      },
      accessToken,
    };
  }

  async validateUser(userId: string): Promise<any> {
    const { data: user, error } = await this.supabaseService
      .getAdminClient()
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error || !user) {
      throw new UnauthorizedException('User not found');
    }

    return user;
  }

  async logout(userId: string): Promise<void> {
    // Sign out from Supabase
    await this.supabaseService.getClient().auth.signOut();
  }



  private async createRoleSpecificRecord(userId: string, role: UserRole, data: any): Promise<void> {
    const supabase = this.supabaseService.getAdminClient();

    switch (role) {
      case 'patient':
        await supabase.from('patients').insert({
          id: userId,
          date_of_birth: data.dateOfBirth,
          emergency_contact: data.emergencyContact,
          assigned_doctor_id: data.assignedDoctorId,
          insurance_id: data.insuranceId,
        });
        
        // Create initial gamification stats
        await supabase.from('gamification_stats').insert({
          patient_id: userId,
        });
        break;

      case 'doctor':
        await supabase.from('doctors').insert({
          id: userId,
          specialization: data.specialization,
          license_number: data.licenseNumber,
          hospital_id: data.hospitalId,
        });
        break;

      case 'hospital':
        await supabase.from('hospitals').insert({
          id: userId,
          address: data.address,
          phone: data.phone,
          website: data.website,
        });
        break;

      case 'insurance':
        await supabase.from('insurance_providers').insert({
          id: userId,
          company_name: data.companyName,
          address: data.address,
          phone: data.phone,
          website: data.website,
          policy_types: data.policyTypes || [],
          coverage_areas: data.coverageAreas || [],
        });
        break;
    }
  }

  async googleSignup(googleSignupDto: GoogleSignupDto, supabaseToken: string): Promise<AuthResponseDto> {
    const { email, name, role, supabaseUserId } = googleSignupDto;

    // Verify the Supabase token and get user info
    const { data: tokenUser, error: tokenError } = await this.supabaseService
      .getClient()
      .auth.getUser(supabaseToken);

    if (tokenError || !tokenUser.user) {
      throw new UnauthorizedException('Invalid Supabase token');
    }

    if (tokenUser.user.email !== email) {
      throw new BadRequestException('Email mismatch with token');
    }

    // Check if user already exists in our database
    const { data: existingUser } = await this.supabaseService
      .getAdminClient()
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    let userData;

    if (existingUser) {
      // User exists - update their supabase_user_id if needed and log them in
      if (!existingUser.supabase_user_id || existingUser.supabase_user_id !== supabaseUserId) {
        const { data: updatedUser, error: updateError } = await this.supabaseService
          .getAdminClient()
          .from('users')
          .update({
            supabase_user_id: supabaseUserId,
            updated_at: new Date().toISOString(),
          })
          .eq('id', existingUser.id)
          .select()
          .single();

        if (updateError) {
          throw new BadRequestException(`Failed to update user: ${updateError.message}`);
        }
        userData = updatedUser;
      } else {
        userData = existingUser;
      }
    } else {
      // Create new user in our database
      const { data: newUser, error: userError } = await this.supabaseService
        .getAdminClient()
        .from('users')
        .insert({
          email,
          name,
          role,
          supabase_user_id: supabaseUserId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (userError) {
        throw new BadRequestException(`Failed to create user: ${userError.message}`);
      }

      userData = newUser;

      // Create role-specific data (for patient role)
      await this.createRoleSpecificRecord(userData.id, role, {});
    }

    // Generate JWT token
    const payload = {
      sub: userData.id,
      email: userData.email,
      role: userData.role,
      name: userData.name
    };
    const accessToken = this.jwtService.sign(payload);

    return {
      user: {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        avatar: userData.avatar,
      },
      accessToken,
    };
  }

}
