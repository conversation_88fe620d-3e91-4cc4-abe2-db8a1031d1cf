-- Migration: Add reminder settings to patients table
-- Date: 2025-01-07
-- Description: Add reminder preference columns to patients table

-- Add reminder settings columns to patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS push_notifications B<PERSON><PERSON><PERSON><PERSON> DEFAULT true,
ADD COLUMN IF NOT EXISTS sms_reminders BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS voice_calls BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS email_reminders B<PERSON><PERSON>EAN DEFAULT true,
ADD COLUMN IF NOT EXISTS reminder_timing INTEGER DEFAULT 15,
ADD COLUMN IF NOT EXISTS snooze_interval INTEGER DEFAULT 15,
ADD COLUMN IF NOT EXISTS escalation_enabled BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS preferred_reminder_type reminder_type DEFAULT 'sms';

-- Update existing patients with default values
UPDATE patients 
SET 
    push_notifications = COALESCE(push_notifications, true),
    sms_reminders = COALESCE(sms_reminders, true),
    voice_calls = COALESCE(voice_calls, false),
    email_reminders = COALESCE(email_reminders, true),
    reminder_timing = COALESCE(reminder_timing, 15),
    snooze_interval = COALESCE(snooze_interval, 15),
    escalation_enabled = COALESCE(escalation_enabled, true),
    preferred_reminder_type = COALESCE(preferred_reminder_type, 'sms'::reminder_type)
WHERE 
    push_notifications IS NULL 
    OR sms_reminders IS NULL 
    OR voice_calls IS NULL 
    OR email_reminders IS NULL 
    OR reminder_timing IS NULL 
    OR snooze_interval IS NULL 
    OR escalation_enabled IS NULL 
    OR preferred_reminder_type IS NULL;
