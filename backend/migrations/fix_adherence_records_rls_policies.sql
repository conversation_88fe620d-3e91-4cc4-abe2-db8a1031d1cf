-- Migration: Fix RLS policies for adherence_records table
-- Date: 2025-07-03
-- Description: Add proper RLS policies for adherence_records table to allow insertion and access

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "adherence_records_select_policy" ON adherence_records;
DROP POLICY IF EXISTS "adherence_records_insert_policy" ON adherence_records;
DROP POLICY IF EXISTS "adherence_records_update_policy" ON adherence_records;
DROP POLICY IF EXISTS "adherence_records_delete_policy" ON adherence_records;

-- Create comprehensive RLS policies for adherence_records table
-- Allow patients to view their own adherence records
CREATE POLICY "adherence_records_select_policy" ON adherence_records
    FOR SELECT USING (
        auth.uid() = patient_id::uuid OR
        auth.uid() IN (
            SELECT d.id::uuid FROM doctors d
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = adherence_records.patient_id
        ) OR
        auth.uid() IN (
            SELECT h.id::uuid FROM hospitals h
            JOIN doctors d ON d.hospital_id = h.id
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = adherence_records.patient_id
        ) OR
        auth.uid() IN (
            SELECT ip.id::uuid FROM insurance_providers ip
            JOIN patients p ON p.insurance_provider_id = ip.id
            WHERE p.id = adherence_records.patient_id
        ) OR
        EXISTS (
            SELECT 1 FROM users u
            WHERE u.id::uuid = auth.uid() AND u.role = 'admin'
        )
    );

-- Allow patients, doctors, hospitals, and admins to insert adherence records
CREATE POLICY "adherence_records_insert_policy" ON adherence_records
    FOR INSERT WITH CHECK (
        auth.uid() = patient_id::uuid OR
        auth.uid() IN (
            SELECT d.id::uuid FROM doctors d
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = adherence_records.patient_id
        ) OR
        auth.uid() IN (
            SELECT h.id::uuid FROM hospitals h
            JOIN doctors d ON d.hospital_id = h.id
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = adherence_records.patient_id
        ) OR
        EXISTS (
            SELECT 1 FROM users u
            WHERE u.id::uuid = auth.uid() AND u.role = 'admin'
        )
    );

-- Allow patients and their doctors to update adherence records
CREATE POLICY "adherence_records_update_policy" ON adherence_records
    FOR UPDATE USING (
        auth.uid() = patient_id::uuid OR
        auth.uid() IN (
            SELECT d.id::uuid FROM doctors d
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = adherence_records.patient_id
        ) OR
        EXISTS (
            SELECT 1 FROM users u
            WHERE u.id::uuid = auth.uid() AND u.role = 'admin'
        )
    );

-- Allow patients and their doctors to delete adherence records
CREATE POLICY "adherence_records_delete_policy" ON adherence_records
    FOR DELETE USING (
        auth.uid() = patient_id::uuid OR
        auth.uid() IN (
            SELECT d.id::uuid FROM doctors d
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = adherence_records.patient_id
        ) OR
        EXISTS (
            SELECT 1 FROM users u
            WHERE u.id::uuid = auth.uid() AND u.role = 'admin'
        )
    );
