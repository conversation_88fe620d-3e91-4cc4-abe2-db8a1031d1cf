const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://ogpzoklypdukcurekbhe.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.fEpTSh_u-PGTYt0Me-9w7SzgHMA247wHG-7wXqmTM1s'
);

async function addColumnsManually() {
  console.log('Adding reminder settings columns manually...');
  
  const userId = 'dfd17757-e959-473c-a507-0b946b82ab79';
  
  try {
    // First, let's check the current patient record
    const { data: currentPatient, error: fetchError } = await supabase
      .from('patients')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (fetchError) {
      console.log('Error fetching patient:', fetchError.message);
      return;
    }
    
    console.log('Current patient record:', currentPatient);
    
    // Since we can't add columns via the client, let's work with what we have
    // and modify the service to handle the missing columns gracefully
    
    console.log('The database columns need to be added manually via Supabase dashboard.');
    console.log('Please execute this SQL in the Supabase SQL editor:');
    console.log(`
-- Step 1: Add phone number and reminder settings columns to patients table
ALTER TABLE patients
ADD COLUMN IF NOT EXISTS phone VARCHAR(50),
ADD COLUMN IF NOT EXISTS push_notifications BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS sms_reminders BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS voice_calls BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS email_reminders BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS reminder_timing INTEGER DEFAULT 15,
ADD COLUMN IF NOT EXISTS snooze_interval INTEGER DEFAULT 15,
ADD COLUMN IF NOT EXISTS escalation_enabled BOOLEAN DEFAULT true;

-- Step 2: Create enum type for preferred_reminder_type (if not exists)
DO $$ BEGIN
    CREATE TYPE reminder_type AS ENUM ('sms', 'call', 'notification');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Step 3: Add the enum column
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS preferred_reminder_type reminder_type DEFAULT 'sms';

-- Step 4: Update existing patients with default values
UPDATE patients
SET
    phone = COALESCE(phone, '+919101004681'), -- Default to user's test number
    push_notifications = COALESCE(push_notifications, true),
    sms_reminders = COALESCE(sms_reminders, true),
    voice_calls = COALESCE(voice_calls, false),
    email_reminders = COALESCE(email_reminders, true),
    reminder_timing = COALESCE(reminder_timing, 15),
    snooze_interval = COALESCE(snooze_interval, 15),
    escalation_enabled = COALESCE(escalation_enabled, true),
    preferred_reminder_type = COALESCE(preferred_reminder_type, 'sms'::reminder_type);
    `);
    
  } catch (err) {
    console.log('Error:', err.message);
  }
}

addColumnsManually().catch(console.error);
