-- Add FCM tokens table for push notification management
-- This table stores Firebase Cloud Messaging tokens for users

CREATE TABLE IF NOT EXISTS fcm_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token TEXT NOT NULL,
    device_type VARCHAR(20) NOT NULL CHECK (device_type IN ('web', 'android', 'ios')),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_fcm_tokens_user_id ON fcm_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_fcm_tokens_active ON fcm_tokens(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_fcm_tokens_last_used ON fcm_tokens(last_used);

-- Create unique constraint to prevent duplicate tokens per user
CREATE UNIQUE INDEX IF NOT EXISTS idx_fcm_tokens_user_token ON fcm_tokens(user_id, token) WHERE is_active = true;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_fcm_tokens_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_fcm_tokens_updated_at
    BEFORE UPDATE ON fcm_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_fcm_tokens_updated_at();

-- Add RLS policies for security
ALTER TABLE fcm_tokens ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own tokens
CREATE POLICY fcm_tokens_user_policy ON fcm_tokens
    FOR ALL USING (auth.uid() = user_id);

-- Policy: Service role can access all tokens (for admin operations)
CREATE POLICY fcm_tokens_service_policy ON fcm_tokens
    FOR ALL USING (auth.role() = 'service_role');

-- Add comments for documentation
COMMENT ON TABLE fcm_tokens IS 'Stores Firebase Cloud Messaging tokens for push notifications';
COMMENT ON COLUMN fcm_tokens.user_id IS 'Reference to the user who owns this token';
COMMENT ON COLUMN fcm_tokens.token IS 'FCM registration token for the device';
COMMENT ON COLUMN fcm_tokens.device_type IS 'Type of device: web, android, or ios';
COMMENT ON COLUMN fcm_tokens.user_agent IS 'Browser/device user agent string for web tokens';
COMMENT ON COLUMN fcm_tokens.is_active IS 'Whether the token is still valid and active';
COMMENT ON COLUMN fcm_tokens.last_used IS 'Last time this token was used for sending notifications';
