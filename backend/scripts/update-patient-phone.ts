import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updatePatientPhone() {
  try {
    console.log('Updating patient phone number...');
    
    const { data, error } = await supabase
      .from('patients')
      .update({ emergency_contact: '+919101004681' })
      .eq('id', 'dfd17757-e959-473c-a507-0b946b82ab79')
      .select();

    if (error) {
      console.error('Error updating patient phone:', error);
      return;
    }

    console.log('Patient phone updated successfully:', data);
    
    // Verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from('patients')
      .select('emergency_contact')
      .eq('id', 'dfd17757-e959-473c-a507-0b946b82ab79')
      .single();

    if (verifyError) {
      console.error('Error verifying update:', verifyError);
      return;
    }

    console.log('Verified emergency contact:', verifyData.emergency_contact);
  } catch (error) {
    console.error('Script error:', error);
  }
}

updatePatientPhone();
