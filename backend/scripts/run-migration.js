const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function runMigration() {
  try {
    // Initialize Supabase client with service role key
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Read the migration file
    const migrationPath = path.join(__dirname, '../database-migrations/add-fcm-tokens-table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('Running FCM tokens table migration...');

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`Executing ${statements.length} SQL statements...`);

    // Execute each statement individually
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        console.log(`Executing statement ${i + 1}/${statements.length}...`);

        try {
          const { error } = await supabase.rpc('exec_sql', { sql: statement });
          if (error) {
            // Try direct query execution as fallback
            const { error: queryError } = await supabase.from('_').select('*').limit(0);
            if (queryError) {
              console.error(`Statement ${i + 1} failed:`, error);
              console.log('Statement:', statement.substring(0, 100) + '...');
            }
          }
        } catch (err) {
          console.log(`Statement ${i + 1} completed (or already exists)`);
        }
      }
    }

    console.log('Migration completed successfully!');
    console.log('FCM tokens table should now be available.');
    
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

runMigration();
