import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { RemindersService } from '../src/modules/reminders/reminders.service';

async function manualCronTrigger() {
  try {
    console.log('🚀 Starting manual cron trigger...');
    
    // Create the NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the RemindersService
    const remindersService = app.get(RemindersService);
    
    console.log('📋 Manually triggering processPendingReminders...');
    
    // Manually call the cron job method
    await remindersService.processPendingReminders();
    
    console.log('✅ Manual cron trigger completed');
    
    // Close the application
    await app.close();
    
  } catch (error) {
    console.error('💥 Error during manual cron trigger:', error);
    process.exit(1);
  }
}

manualCronTrigger().catch(console.error);
