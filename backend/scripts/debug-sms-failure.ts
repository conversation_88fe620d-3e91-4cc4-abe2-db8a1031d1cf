import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { TwilioService } from '../src/common/services/twilio.service';
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugSMSFailure() {
  try {
    console.log('🔍 Debugging SMS failure...\n');

    // Create the NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the TwilioService
    const twilioService = app.get(TwilioService);
    
    // First, test if Twilio service is working
    console.log('📞 Testing Twilio service status...');
    const twilioStatus = await twilioService.getServiceStatus();
    console.log(`Twilio Status: ${JSON.stringify(twilioStatus, null, 2)}`);
    
    if (!twilioStatus.enabled) {
      console.log('❌ Twilio is not enabled!');
      await app.close();
      return;
    }

    // Get a recent failed reminder to debug
    console.log('\n🔍 Getting recent failed reminder...');
    const { data: failedReminders, error } = await supabase
      .from('reminders')
      .select(`
        *,
        patient:patients(id, emergency_contact, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency)
      `)
      .eq('status', 'failed')
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('❌ Error fetching failed reminders:', error);
      await app.close();
      return;
    }

    if (!failedReminders || failedReminders.length === 0) {
      console.log('❌ No failed reminders found');
      await app.close();
      return;
    }

    const reminder = failedReminders[0];
    console.log(`\n📝 Debugging reminder: ${reminder.id}`);
    console.log(`   Message: ${reminder.message}`);
    console.log(`   Type: ${reminder.reminder_type}`);
    console.log(`   Scheduled: ${reminder.scheduled_time}`);
    
    // Extract patient data
    console.log(`\n👤 Patient Data:`);
    console.log(`   Patient ID: ${reminder.patient_id}`);
    console.log(`   Patient Object: ${JSON.stringify(reminder.patient, null, 2)}`);
    
    const phoneNumber = reminder.patient?.emergency_contact;
    const patientName = reminder.patient?.users?.name || 'Patient';
    const patientEmail = reminder.patient?.users?.email || '';
    
    console.log(`   Extracted Phone: ${phoneNumber}`);
    console.log(`   Extracted Name: ${patientName}`);
    console.log(`   Extracted Email: ${patientEmail}`);
    
    // Extract medicine data
    console.log(`\n💊 Medicine Data:`);
    console.log(`   Medicine ID: ${reminder.medicine_id}`);
    console.log(`   Medicine Object: ${JSON.stringify(reminder.medicine, null, 2)}`);
    
    const medicineName = reminder.medicine?.name || 'your medication';
    const dosage = reminder.medicine?.dosage || '';
    
    console.log(`   Extracted Medicine Name: ${medicineName}`);
    console.log(`   Extracted Dosage: ${dosage}`);
    
    // Check if SMS should be sent
    const shouldSendSMS = reminder.reminder_type === 'sms' && phoneNumber;
    console.log(`\n📱 SMS Check:`);
    console.log(`   Reminder Type: ${reminder.reminder_type}`);
    console.log(`   Phone Number Available: ${!!phoneNumber}`);
    console.log(`   Should Send SMS: ${shouldSendSMS}`);
    
    if (!shouldSendSMS) {
      if (reminder.reminder_type !== 'sms') {
        console.log(`❌ Not an SMS reminder (type: ${reminder.reminder_type})`);
      } else {
        console.log(`❌ No phone number available for SMS`);
      }
      await app.close();
      return;
    }
    
    // Try to send SMS
    console.log(`\n📤 Attempting to send SMS...`);
    console.log(`   To: ${phoneNumber}`);
    console.log(`   Patient: ${patientName}`);
    console.log(`   Medicine: ${medicineName}`);
    console.log(`   Dosage: ${dosage}`);
    
    try {
      const smsResult = await twilioService.sendMedicationReminder(
        phoneNumber,
        patientName,
        medicineName,
        dosage,
        'sms'
      );
      
      console.log(`\n📤 SMS Result: ${JSON.stringify(smsResult, null, 2)}`);
      
      if (smsResult.success) {
        console.log(`✅ SMS sent successfully!`);
      } else {
        console.log(`❌ SMS failed: ${smsResult.error}`);
      }
      
    } catch (error) {
      console.log(`💥 SMS Exception: ${error.message}`);
      console.log(`   Stack: ${error.stack}`);
    }
    
    // Close the application
    await app.close();
    
  } catch (error) {
    console.error('💥 Error during SMS debug:', error);
    process.exit(1);
  }
}

debugSMSFailure().catch(console.error);
