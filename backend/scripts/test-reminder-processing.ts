import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testReminderProcessing() {
  try {
    console.log('🔍 Testing reminder processing logic...\n');

    // Get current time
    const now = new Date();
    console.log(`⏰ Current time: ${now.toISOString()}`);

    // Query for due reminders (same query as in the service)
    const { data: dueReminders, error } = await supabase
      .from('reminders')
      .select(`
        *,
        patient:patients(id, emergency_contact, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency)
      `)
      .eq('status', 'pending')
      .eq('is_active', true)
      .lte('scheduled_time', now.toISOString());

    if (error) {
      console.error('❌ Error fetching due reminders:', error);
      return;
    }

    console.log(`📋 Found ${dueReminders?.length || 0} due reminders:`);
    
    if (dueReminders && dueReminders.length > 0) {
      dueReminders.forEach((reminder, index) => {
        console.log(`\n📝 Reminder ${index + 1}:`);
        console.log(`   ID: ${reminder.id}`);
        console.log(`   Patient ID: ${reminder.patient_id}`);
        console.log(`   Medicine ID: ${reminder.medicine_id}`);
        console.log(`   Type: ${reminder.reminder_type}`);
        console.log(`   Scheduled: ${reminder.scheduled_time}`);
        console.log(`   Message: ${reminder.message}`);
        console.log(`   Status: ${reminder.status}`);
        
        // Check patient data
        if (reminder.patient) {
          console.log(`   Patient Phone: ${reminder.patient.emergency_contact}`);
          if (reminder.patient.users) {
            console.log(`   Patient Name: ${reminder.patient.users.name}`);
            console.log(`   Patient Email: ${reminder.patient.users.email}`);
          }
        } else {
          console.log(`   ❌ Patient data missing!`);
        }
        
        // Check medicine data
        if (reminder.medicine) {
          console.log(`   Medicine Name: ${reminder.medicine.name}`);
          console.log(`   Medicine Dosage: ${reminder.medicine.dosage}`);
        } else {
          console.log(`   ❌ Medicine data missing!`);
        }
      });
    } else {
      console.log('   No due reminders found');
    }

    // Check if there are any pending reminders at all
    const { data: allPendingReminders, error: allError } = await supabase
      .from('reminders')
      .select('*')
      .eq('status', 'pending')
      .eq('is_active', true);

    if (allError) {
      console.error('❌ Error fetching all pending reminders:', allError);
    } else {
      console.log(`\n📊 Total pending reminders: ${allPendingReminders?.length || 0}`);
      if (allPendingReminders && allPendingReminders.length > 0) {
        console.log('   Scheduled times:');
        allPendingReminders.forEach(r => {
          console.log(`   - ${r.id}: ${r.scheduled_time} (${r.message})`);
        });
      }
    }

  } catch (error) {
    console.error('💥 Error:', error);
  }
}

testReminderProcessing().catch(console.error);
