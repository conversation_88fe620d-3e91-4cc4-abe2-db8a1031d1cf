const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function createFCMTable() {
  try {
    console.log('Creating FCM tokens table...');
    
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Create the FCM tokens table
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS fcm_tokens (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        token TEXT NOT NULL,
        device_type VARCHAR(20) NOT NULL CHECK (device_type IN ('web', 'android', 'ios')),
        user_agent TEXT,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, token)
      );
    `;

    const { error: createError } = await supabase.rpc('exec_sql', { 
      sql: createTableSQL 
    });

    if (createError) {
      console.error('Error creating table:', createError);
      
      // Try alternative approach - direct query
      const { error: directError } = await supabase
        .from('fcm_tokens')
        .select('count')
        .limit(1);
        
      if (directError && directError.code === '42P01') {
        console.log('Table does not exist. Please run this SQL manually in Supabase SQL editor:');
        console.log(createTableSQL);
        return false;
      }
    } else {
      console.log('✓ FCM tokens table created successfully');
    }

    // Create indexes
    const createIndexesSQL = `
      CREATE INDEX IF NOT EXISTS idx_fcm_tokens_user_id ON fcm_tokens(user_id);
      CREATE INDEX IF NOT EXISTS idx_fcm_tokens_active ON fcm_tokens(is_active);
      CREATE INDEX IF NOT EXISTS idx_fcm_tokens_device_type ON fcm_tokens(device_type);
    `;

    const { error: indexError } = await supabase.rpc('exec_sql', { 
      sql: createIndexesSQL 
    });

    if (indexError) {
      console.log('⚠ Could not create indexes automatically. Please run this SQL manually:');
      console.log(createIndexesSQL);
    } else {
      console.log('✓ FCM tokens indexes created successfully');
    }

    // Enable RLS
    const enableRLSSQL = `
      ALTER TABLE fcm_tokens ENABLE ROW LEVEL SECURITY;
      
      CREATE POLICY IF NOT EXISTS "Users can manage their own FCM tokens" ON fcm_tokens
        FOR ALL USING (auth.uid() = user_id);
    `;

    const { error: rlsError } = await supabase.rpc('exec_sql', { 
      sql: enableRLSSQL 
    });

    if (rlsError) {
      console.log('⚠ Could not enable RLS automatically. Please run this SQL manually:');
      console.log(enableRLSSQL);
    } else {
      console.log('✓ FCM tokens RLS policies created successfully');
    }

    // Test table access
    const { data, error: testError } = await supabase
      .from('fcm_tokens')
      .select('count')
      .limit(1);

    if (testError) {
      console.error('⚠ Table created but cannot access:', testError.message);
      return false;
    } else {
      console.log('✓ FCM tokens table is accessible');
      return true;
    }

  } catch (error) {
    console.error('Error creating FCM table:', error);
    return false;
  }
}

createFCMTable().then(success => {
  if (success) {
    console.log('\n🎉 FCM tokens table setup completed successfully!');
  } else {
    console.log('\n⚠ FCM tokens table setup needs manual intervention.');
    console.log('Please run the SQL commands shown above in your Supabase SQL editor.');
  }
});
