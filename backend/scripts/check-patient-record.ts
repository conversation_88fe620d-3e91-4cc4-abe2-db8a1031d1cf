import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function checkPatientRecord() {
  console.log('🔍 Checking patient record...');
  
  const patientId = 'dfd17757-e959-473c-a507-0b946b82ab79';
  
  // Check if patient record exists
  const { data: patientData, error: patientError } = await supabase
    .from('patients')
    .select('*')
    .eq('id', patientId)
    .single();
  
  console.log('📋 Patient data:', patientData);
  if (patientError) {
    console.log('❌ Patient error:', patientError);
  }
  
  // Also check user record
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('id', patientId)
    .single();
  
  console.log('👤 User data:', userData);
  if (userError) {
    console.log('❌ User error:', userError);
  }
  
  // Test the exact query used in the reminder service
  console.log('\n🔍 Testing reminder service query...');
  const { data: patientDetails, error: detailsError } = await supabase
    .from('patients')
    .select('emergency_contact')
    .eq('id', patientId)
    .single();
  
  console.log('📱 Emergency contact:', patientDetails?.emergency_contact);
  if (detailsError) {
    console.log('❌ Details error:', detailsError);
  }
}

checkPatientRecord().catch(console.error);
