require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

async function fixAdherenceRLS() {
  console.log('🔧 Fixing adherence_records RLS policies...');

  // Create Supabase client with service role key
  const supabaseUrl = process.env.SUPABASE_URL || 'https://ogpzoklypdukcurekbhe.supabase.co';
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!serviceRoleKey) {
    console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    console.log('📄 Running migration SQL statements...');

    // Drop existing policies
    console.log('🗑️ Dropping existing policies...');
    const dropPolicies = [
      'DROP POLICY IF EXISTS "adherence_records_select_policy" ON adherence_records',
      'DROP POLICY IF EXISTS "adherence_records_insert_policy" ON adherence_records',
      'DROP POLICY IF EXISTS "adherence_records_update_policy" ON adherence_records',
      'DROP POLICY IF EXISTS "adherence_records_delete_policy" ON adherence_records'
    ];

    for (const sql of dropPolicies) {
      const { error } = await supabase.rpc('exec', { sql });
      if (error) {
        console.log(`⚠️ Could not drop policy: ${error.message}`);
      }
    }

    // Create new policies
    console.log('🔐 Creating new RLS policies...');

    // SELECT policy
    const selectPolicy = `
      CREATE POLICY "adherence_records_select_policy" ON adherence_records
        FOR SELECT USING (
          auth.uid() = patient_id::uuid OR
          auth.uid() IN (
            SELECT d.id::uuid FROM doctors d
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = adherence_records.patient_id
          ) OR
          auth.uid() IN (
            SELECT h.id::uuid FROM hospitals h
            JOIN doctors d ON d.hospital_id = h.id
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = adherence_records.patient_id
          ) OR
          auth.uid() IN (
            SELECT ip.id::uuid FROM insurance_providers ip
            JOIN patients p ON p.insurance_provider_id = ip.id
            WHERE p.id = adherence_records.patient_id
          ) OR
          EXISTS (
            SELECT 1 FROM users u
            WHERE u.id::uuid = auth.uid() AND u.role = 'admin'
          )
        )
    `;

    const { error: selectError } = await supabase.rpc('exec', { sql: selectPolicy });
    if (selectError) {
      console.error('❌ Failed to create SELECT policy:', selectError);
    } else {
      console.log('✅ SELECT policy created');
    }

    // INSERT policy
    const insertPolicy = `
      CREATE POLICY "adherence_records_insert_policy" ON adherence_records
        FOR INSERT WITH CHECK (
          auth.uid() = patient_id::uuid OR
          auth.uid() IN (
            SELECT d.id::uuid FROM doctors d
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = adherence_records.patient_id
          ) OR
          auth.uid() IN (
            SELECT h.id::uuid FROM hospitals h
            JOIN doctors d ON d.hospital_id = h.id
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = adherence_records.patient_id
          ) OR
          EXISTS (
            SELECT 1 FROM users u
            WHERE u.id::uuid = auth.uid() AND u.role = 'admin'
          )
        )
    `;

    const { error: insertPolicyError } = await supabase.rpc('exec', { sql: insertPolicy });
    if (insertPolicyError) {
      console.error('❌ Failed to create INSERT policy:', insertPolicyError);
    } else {
      console.log('✅ INSERT policy created');
    }

    console.log('✅ Migration completed successfully!');

    // Test the policies by trying to insert a test record
    console.log('\n🧪 Testing adherence record insertion...');
    
    // First, get a test patient ID
    const { data: patients, error: patientsError } = await supabase
      .from('patients')
      .select('id')
      .limit(1);

    if (patientsError || !patients || patients.length === 0) {
      console.log('⚠️ No patients found for testing');
      return;
    }

    const testPatientId = patients[0].id;
    console.log(`👤 Using test patient ID: ${testPatientId}`);

    // Get a test medicine for this patient
    const { data: medicines, error: medicinesError } = await supabase
      .from('medicines')
      .select('id')
      .eq('patient_id', testPatientId)
      .limit(1);

    if (medicinesError || !medicines || medicines.length === 0) {
      console.log('⚠️ No medicines found for test patient');
      return;
    }

    const testMedicineId = medicines[0].id;
    console.log(`💊 Using test medicine ID: ${testMedicineId}`);

    // Try to insert a test adherence record
    const testRecord = {
      patient_id: testPatientId,
      medicine_id: testMedicineId,
      scheduled_time: new Date().toISOString(),
      taken_time: new Date().toISOString(),
      status: 'taken',
      notes: 'Test record from RLS fix script'
    };

    const { data: insertResult, error: testInsertError } = await supabase
      .from('adherence_records')
      .insert(testRecord)
      .select()
      .single();

    if (testInsertError) {
      console.error('❌ Test insertion failed:', testInsertError);
    } else {
      console.log('✅ Test insertion successful!');
      console.log('📊 Inserted record:', insertResult);

      // Clean up test record
      const { error: deleteError } = await supabase
        .from('adherence_records')
        .delete()
        .eq('id', insertResult.id);

      if (deleteError) {
        console.log('⚠️ Could not clean up test record:', deleteError);
      } else {
        console.log('🧹 Test record cleaned up');
      }
    }

  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

fixAdherenceRLS();
