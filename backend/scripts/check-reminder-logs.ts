import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkReminderLogs() {
  try {
    console.log('Checking reminder logs for failed reminder...');
    
    // Get logs for the specific reminder
    const { data: logs, error } = await supabase
      .from('reminder_logs')
      .select('*')
      .eq('reminder_id', 'bb733252-9900-4828-b5b6-c4dde4e634cf')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching reminder logs:', error);
      return;
    }

    console.log('📋 Reminder logs:', JSON.stringify(logs, null, 2));
    
    // Also check all recent logs
    const { data: recentLogs, error: recentError } = await supabase
      .from('reminder_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (recentError) {
      console.error('Error fetching recent logs:', recentError);
      return;
    }

    console.log('\n📋 Recent reminder logs (last 10):');
    recentLogs?.forEach((log, index) => {
      console.log(`${index + 1}. Reminder ID: ${log.reminder_id}`);
      console.log(`   Status: ${log.status}`);
      console.log(`   Sent at: ${log.sent_at}`);
      console.log(`   Error: ${log.error_message || 'None'}`);
      console.log(`   Response: ${JSON.stringify(log.response_data)}`);
      console.log('');
    });
  } catch (error) {
    console.error('Script error:', error);
  }
}

checkReminderLogs();
