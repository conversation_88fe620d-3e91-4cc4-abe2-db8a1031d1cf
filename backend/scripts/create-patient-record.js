const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

const supabase = createClient(supabaseUrl, supabaseKey);

async function createPatientRecord() {
  console.log('🏥 Creating patient record...');
  
  const patientId = 'dfd17757-e959-473c-a507-0b946b82ab79';
  
  // Check if patient record already exists
  const { data: existingPatient, error: checkError } = await supabase
    .from('patients')
    .select('id')
    .eq('id', patientId)
    .single();
  
  if (existingPatient) {
    console.log('✅ Patient record already exists');
  } else {
    // Create patient record
    const { data: patientData, error: patientError } = await supabase
      .from('patients')
      .insert({
        id: patientId,
        date_of_birth: '1990-01-01',
        emergency_contact: '+**********',
      })
      .select()
      .single();

    if (patientError) {
      console.log('❌ Error creating patient record:', patientError);
      return;
    }

    console.log('✅ Patient record created:', patientData);
  }

  
  // Now create some test adherence records
  console.log('💊 Creating test adherence records...');
  
  // Get some medicine IDs for this patient (get the most recent ones)
  const { data: medicines, error: medicinesError } = await supabase
    .from('medicines')
    .select('id, name, created_at')
    .eq('patient_id', patientId)
    .order('created_at', { ascending: false })
    .limit(3);
  
  if (medicinesError || !medicines || medicines.length === 0) {
    console.log('❌ No medicines found for patient');
    return;
  }
  
  console.log(`📋 Found ${medicines.length} medicines for patient`);
  console.log('Medicine IDs:', medicines.map(m => ({ id: m.id, name: m.name, created_at: m.created_at })));

  // First, delete any existing adherence records for these medicines
  console.log('🗑️ Cleaning up existing adherence records...');
  for (const medicine of medicines) {
    const { error: deleteError } = await supabase
      .from('adherence_records')
      .delete()
      .eq('medicine_id', medicine.id);

    if (deleteError) {
      console.log(`❌ Error deleting adherence records for ${medicine.name}:`, deleteError);
    }
  }
  
  // Create adherence records for the past few days
  const adherenceRecords = [];
  const today = new Date();
  
  for (let i = 0; i < 5; i++) {
    const recordDate = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
    
    for (const medicine of medicines) {
      const status = Math.random() > 0.2 ? 'taken' : 'missed';
      const scheduledTime = new Date(recordDate);
      scheduledTime.setHours(9, 0, 0, 0); // 9 AM
      
      const takenTime = status === 'taken' ? 
        new Date(scheduledTime.getTime() + Math.random() * 30 * 60 * 1000) : // Within 30 minutes
        null;
      
      adherenceRecords.push({
        patient_id: patientId,
        medicine_id: medicine.id,
        scheduled_time: scheduledTime.toISOString(),
        taken_time: takenTime ? takenTime.toISOString() : null,
        status,
        notes: status === 'taken' ? 'Taken as prescribed' : 'Forgot to take',
      });
    }
  }
  
  // Insert adherence records
  const { data: adherenceData, error: adherenceError } = await supabase
    .from('adherence_records')
    .insert(adherenceRecords)
    .select();
  
  if (adherenceError) {
    console.log('❌ Error creating adherence records:', adherenceError);
    return;
  }
  
  console.log(`✅ Created ${adherenceData.length} adherence records`);
  console.log('🎉 Setup complete!');
}

createPatientRecord().catch(console.error);
