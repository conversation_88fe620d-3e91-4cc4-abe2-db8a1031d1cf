require('dotenv').config();
const axios = require('axios');

async function testProductionReadiness() {
  console.log('🚀 PRODUCTION READINESS TEST - Medical Adherence Platform');
  console.log('=' .repeat(60));
  
  const baseURL = 'http://localhost:3001/api/v1';
  let allTestsPassed = true;
  
  try {
    // Step 1: Authentication Test
    console.log('\n🔐 AUTHENTICATION TEST');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const { accessToken, user } = loginResponse.data;
    console.log('✅ Login successful');
    console.log(`   Patient: ${user.name} (${user.email})`);
    
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    };
    
    // Step 2: Medicine Management Test
    console.log('\n💊 MEDICINE MANAGEMENT TEST');
    const medicinesResponse = await axios.get(`${baseURL}/medicines`, { headers });
    const medicines = medicinesResponse.data;
    console.log(`✅ Medicines retrieved: ${medicines.length} medicines found`);
    
    if (medicines.length === 0) {
      console.log('⚠️  No medicines found - this may affect other tests');
      allTestsPassed = false;
    }
    
    // Step 3: Medicine Intake Test (CRITICAL FOR PRODUCTION)
    console.log('\n📝 MEDICINE INTAKE TEST (CRITICAL)');
    if (medicines.length > 0) {
      const testMedicine = medicines[0];
      console.log(`   Testing with: ${testMedicine.name}`);
      
      const intakeData = {
        takenTime: new Date().toISOString(),
        notes: 'Production readiness test'
      };
      
      try {
        const intakeResponse = await axios.post(
          `${baseURL}/medicines/${testMedicine.id}/take`,
          intakeData,
          { headers }
        );
        
        console.log('✅ Medicine intake WORKING - PRODUCTION READY!');
        console.log(`   Adherence record created: ${intakeResponse.data.adherenceRecord.id}`);
        
        // Test other intake actions
        const missData = { reason: 'Forgot to take', notes: 'Test missed dose' };
        const missResponse = await axios.post(
          `${baseURL}/medicines/${testMedicine.id}/miss`,
          missData,
          { headers }
        );
        console.log('✅ Mark as missed WORKING');
        
        const skipData = { reason: 'Doctor advised to skip', notes: 'Test skip dose' };
        const skipResponse = await axios.post(
          `${baseURL}/medicines/${testMedicine.id}/skip`,
          skipData,
          { headers }
        );
        console.log('✅ Skip dose WORKING');
        
      } catch (intakeError) {
        console.error('❌ CRITICAL: Medicine intake FAILED - NOT PRODUCTION READY!');
        console.error(`   Error: ${intakeError.response?.data?.message || intakeError.message}`);
        allTestsPassed = false;
      }
    }
    
    // Step 4: Reminders System Test
    console.log('\n⏰ REMINDERS SYSTEM TEST');
    try {
      const remindersResponse = await axios.get(`${baseURL}/reminders`, { headers });
      const reminders = remindersResponse.data;
      console.log(`✅ Reminders system WORKING: ${reminders.length} reminders found`);
      
      // Test upcoming reminders
      const upcomingResponse = await axios.get(`${baseURL}/reminders/upcoming`, { headers });
      console.log(`✅ Upcoming reminders WORKING: ${upcomingResponse.data.length} upcoming`);
      
    } catch (remindersError) {
      console.error('❌ Reminders system FAILED');
      console.error(`   Error: ${remindersError.response?.data?.message || remindersError.message}`);
      allTestsPassed = false;
    }
    
    // Step 5: Adherence Tracking Test
    console.log('\n📊 ADHERENCE TRACKING TEST');
    try {
      const adherenceResponse = await axios.get(`${baseURL}/adherence`, { headers });
      console.log(`✅ Adherence tracking WORKING: ${adherenceResponse.data.length} records found`);
      
    } catch (adherenceError) {
      console.error('❌ Adherence tracking FAILED');
      console.error(`   Error: ${adherenceError.response?.data?.message || adherenceError.message}`);
      allTestsPassed = false;
    }
    
    // Step 6: Database Connectivity Test
    console.log('\n🗄️  DATABASE CONNECTIVITY TEST');
    try {
      // Test multiple endpoints to verify database connectivity
      await Promise.all([
        axios.get(`${baseURL}/medicines`, { headers }),
        axios.get(`${baseURL}/reminders`, { headers }),
        axios.get(`${baseURL}/adherence`, { headers })
      ]);
      console.log('✅ Database connectivity WORKING');
      
    } catch (dbError) {
      console.error('❌ Database connectivity FAILED');
      allTestsPassed = false;
    }
    
    // Final Assessment
    console.log('\n' + '=' .repeat(60));
    console.log('🎯 PRODUCTION READINESS ASSESSMENT');
    console.log('=' .repeat(60));
    
    if (allTestsPassed) {
      console.log('🎉 ✅ PRODUCTION READY!');
      console.log('   All critical features are working:');
      console.log('   • Patient authentication ✅');
      console.log('   • Medicine management ✅');
      console.log('   • Medicine intake recording ✅');
      console.log('   • Reminders system ✅');
      console.log('   • Adherence tracking ✅');
      console.log('   • Database connectivity ✅');
      console.log('\n🚀 The platform is ready for deployment and production use!');
    } else {
      console.log('⚠️  PRODUCTION READINESS ISSUES DETECTED');
      console.log('   Please review the failed tests above before deployment.');
    }
    
  } catch (error) {
    console.error('💥 CRITICAL ERROR during production readiness test:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error:', error.response.data);
    }
    allTestsPassed = false;
  }
  
  return allTestsPassed;
}

// Run the test
testProductionReadiness().then(success => {
  process.exit(success ? 0 : 1);
});
