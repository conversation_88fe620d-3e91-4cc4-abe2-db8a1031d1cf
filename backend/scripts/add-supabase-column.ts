import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SupabaseService } from '../src/config/supabase.service';

async function addSupabaseUserIdColumn() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const supabaseService = app.get(SupabaseService);
  const supabase = supabaseService.getAdminClient();

  try {
    console.log('🔧 Adding supabase_user_id column to users table...');

    // First, let's check if the column already exists
    const { data: columns, error: checkError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'users')
      .eq('column_name', 'supabase_user_id');

    if (checkError) {
      console.log('⚠️ Could not check existing columns, proceeding with ALTER TABLE...');
    } else if (columns && columns.length > 0) {
      console.log('✅ Column supabase_user_id already exists in users table');
      await app.close();
      return;
    }

    // Try to add the column by directly querying the database
    // Since we can't use rpc('exec_sql'), we'll try a different approach
    console.log('🔧 Attempting to add column via direct database modification...');

    // Let's try to insert a test record to see the current schema
    const { data: testData, error: testError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (testError) {
      console.error('❌ Error accessing users table:', testError);
    } else {
      console.log('📋 Current users table structure sample:', testData?.[0] ? Object.keys(testData[0]) : 'No data');
    }

    console.log('⚠️ Manual database schema update required');
    console.log('📋 Please run this SQL command in your Supabase SQL Editor:');
    console.log('   ALTER TABLE users ADD COLUMN IF NOT EXISTS supabase_user_id VARCHAR(255) UNIQUE;');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  } finally {
    await app.close();
  }
}

addSupabaseUserIdColumn();
