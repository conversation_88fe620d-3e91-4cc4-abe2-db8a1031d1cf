const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Medicine name parsing function (same as in backend)
function parseMedicineName(fullName) {
  const cleanName = fullName.trim();
  
  // Remove common prefixes
  let name = cleanName
    .replace(/^(TAB\.|TABLET|CAP\.|CAPSULE|SYR\.|SYRUP|INJ\.|INJECTION)\s*/i, '')
    .trim();
  
  // Extract dosage (numbers followed by units)
  const dosageMatch = name.match(/(\d+(?:\.\d+)?\s*(?:MG|MCG|G|ML|IU|%)\b)/i);
  let dosage = 'As prescribed';
  
  if (dosageMatch) {
    dosage = dosageMatch[1].toUpperCase();
    // Remove dosage from name
    name = name.replace(dosageMatch[0], '').trim();
  }
  
  // Clean up any remaining artifacts
  name = name.replace(/\s+/g, ' ').trim();
  
  return { name, dosage };
}

async function fixMissingMedicines() {
  console.log('🔧 Fixing missing medicine records...');
  
  try {
    // Get prescriptions with AI extraction but no medicine records
    const { data: prescriptions, error: prescError } = await supabase
      .from('prescriptions')
      .select('id, patient_id, ai_extracted_medicines, ai_extraction_success')
      .eq('ai_extraction_success', true)
      .not('ai_extracted_medicines', 'is', null);
    
    if (prescError) {
      console.error('❌ Error fetching prescriptions:', prescError);
      return;
    }
    
    console.log(`📋 Found ${prescriptions.length} prescriptions with AI extraction`);
    
    for (const prescription of prescriptions) {
      // Check if medicines already exist for this prescription
      const { data: existingMedicines, error: medError } = await supabase
        .from('medicines')
        .select('id')
        .eq('prescription_id', prescription.id);
      
      if (medError) {
        console.error(`❌ Error checking existing medicines for ${prescription.id}:`, medError);
        continue;
      }
      
      if (existingMedicines.length > 0) {
        console.log(`✅ Prescription ${prescription.id} already has ${existingMedicines.length} medicines`);
        continue;
      }
      
      console.log(`🔧 Creating medicines for prescription ${prescription.id}...`);
      
      // Set default dates (start today, end in 30 days)
      const startDate = new Date().toISOString().split('T')[0];
      const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      const medicinesToCreate = [];
      
      for (const medicineName of prescription.ai_extracted_medicines) {
        const parsedMedicine = parseMedicineName(medicineName);
        
        medicinesToCreate.push({
          name: parsedMedicine.name,
          dosage: parsedMedicine.dosage,
          frequency: 'Once daily',
          duration: '30',
          instructions: 'Take as directed by your doctor',
          start_date: startDate,
          end_date: endDate,
          prescription_id: prescription.id,
          patient_id: prescription.patient_id,
          is_active: true,
        });
      }
      
      if (medicinesToCreate.length > 0) {
        const { data: createdMedicines, error: createError } = await supabase
          .from('medicines')
          .insert(medicinesToCreate)
          .select();
        
        if (createError) {
          console.error(`❌ Error creating medicines for ${prescription.id}:`, createError);
        } else {
          console.log(`✅ Created ${createdMedicines.length} medicines for prescription ${prescription.id}`);
          createdMedicines.forEach((med, i) => {
            console.log(`   ${i+1}. ${med.name} (${med.dosage})`);
          });
        }
      }
    }
    
    console.log('\n🎉 Medicine fixing completed!');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

fixMissingMedicines();
