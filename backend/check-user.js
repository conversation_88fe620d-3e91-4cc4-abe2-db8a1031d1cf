const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

const supabase = createClient(
  'https://ogpzoklypdukcurekbhe.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.fEpTSh_u-PGTYt0Me-9w7SzgHMA247wHG-7wXqmTM1s'
);

async function runMigration() {
  console.log('Running migration to add reminder settings columns...');

  try {
    // Check if columns already exist by trying to select them
    const { data: testData, error: testError } = await supabase
      .from('patients')
      .select('push_notifications, sms_reminders, voice_calls, email_reminders')
      .limit(1);

    if (!testError) {
      console.log('Columns already exist, skipping migration');
      return;
    }

    console.log('Columns do not exist, need to add them manually via Supabase dashboard');
    console.log('Please run the following SQL in the Supabase SQL editor:');
    console.log(`
-- Add reminder settings columns to patients table
ALTER TABLE patients
ADD COLUMN push_notifications BOOLEAN DEFAULT true,
ADD COLUMN sms_reminders BOOLEAN DEFAULT true,
ADD COLUMN voice_calls BOOLEAN DEFAULT false,
ADD COLUMN email_reminders BOOLEAN DEFAULT true,
ADD COLUMN reminder_timing INTEGER DEFAULT 15,
ADD COLUMN snooze_interval INTEGER DEFAULT 15,
ADD COLUMN escalation_enabled BOOLEAN DEFAULT true;

-- Create enum type for preferred_reminder_type
CREATE TYPE reminder_type AS ENUM ('sms', 'call', 'notification');

-- Add the enum column
ALTER TABLE patients
ADD COLUMN preferred_reminder_type reminder_type DEFAULT 'sms';

-- Update existing patients with default values
UPDATE patients
SET
    push_notifications = true,
    sms_reminders = true,
    voice_calls = false,
    email_reminders = true,
    reminder_timing = 15,
    snooze_interval = 15,
    escalation_enabled = true,
    preferred_reminder_type = 'sms'::reminder_type;
    `);

  } catch (err) {
    console.log('Error running migration:', err.message);
  }
}

async function checkUser() {
  const userId = 'dfd17757-e959-473c-a507-0b946b82ab79';

  console.log('Checking user:', userId);

  // Check patients table
  const { data: patient, error: patientError } = await supabase
    .from('patients')
    .select('*')
    .eq('id', userId)
    .single();

  console.log('Patient found:', !!patient);
  if (patient) {
    console.log('Patient has reminder settings columns:', {
      push_notifications: patient.push_notifications,
      sms_reminders: patient.sms_reminders,
      voice_calls: patient.voice_calls,
      email_reminders: patient.email_reminders
    });
  }
  if (patientError) console.log('Patient Error:', patientError.message);
}

async function main() {
  await runMigration();
  await checkUser();
}

main().catch(console.error);
