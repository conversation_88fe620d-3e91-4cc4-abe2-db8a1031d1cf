-- Add phone column to patients table for voice call functionality
-- Execute this in Supabase SQL Editor

-- Step 1: Add phone column to patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS phone VARCHAR(50);

-- Step 2: Update existing patients with default phone number
UPDATE patients 
SET phone = COALESCE(phone, '+919101004681')
WHERE phone IS NULL;

-- Step 3: Verify the column was added
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'patients' AND column_name = 'phone';

-- Step 4: Check current patient record
SELECT id, phone, push_notifications, sms_reminders, voice_calls, email_reminders
FROM patients 
WHERE id = 'dfd17757-e959-473c-a507-0b946b82ab79';
