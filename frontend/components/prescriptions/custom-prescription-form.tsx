'use client';

import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Pill, 
  Plus, 
  Trash2, 
  Calendar, 
  Clock, 
  FileText, 
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { MedicineService } from '@/lib/api/medicines';
import { useAuthStore } from '@/lib/store';
import { toast } from 'sonner';

interface ExtractedMedicine {
  name: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions: string;
  startDate: string;
  endDate: string;
}

interface CustomPrescriptionFormProps {
  isOpen: boolean;
  onClose: () => void;
  prescriptionId: string;
  extractedMedicines: string[];
  onSuccess?: () => void;
}

const FREQUENCY_OPTIONS = [
  { value: 'Once daily', label: 'Once daily (1 time per day)' },
  { value: 'Twice daily', label: 'Twice daily (2 times per day)' },
  { value: 'Three times daily', label: 'Three times daily (3 times per day)' },
  { value: 'Four times daily', label: 'Four times daily (4 times per day)' },
  { value: 'Every 6 hours', label: 'Every 6 hours (4 times per day)' },
  { value: 'Every 8 hours', label: 'Every 8 hours (3 times per day)' },
  { value: 'Every 12 hours', label: 'Every 12 hours (2 times per day)' },
  { value: 'As needed', label: 'As needed (PRN)' },
  { value: 'Weekly', label: 'Weekly' },
  { value: 'Custom', label: 'Custom frequency' },
];

const DURATION_OPTIONS = [
  { value: '3', label: '3 days' },
  { value: '5', label: '5 days' },
  { value: '7', label: '1 week' },
  { value: '10', label: '10 days' },
  { value: '14', label: '2 weeks' },
  { value: '21', label: '3 weeks' },
  { value: '30', label: '1 month' },
  { value: '60', label: '2 months' },
  { value: '90', label: '3 months' },
  { value: 'custom', label: 'Custom duration' },
];

export function CustomPrescriptionForm({
  isOpen,
  onClose,
  prescriptionId,
  extractedMedicines,
  onSuccess,
}: CustomPrescriptionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuthStore();

  // Initialize form with extracted medicines or empty array
  const initializeMedicines = () => {
    if (!extractedMedicines || extractedMedicines.length === 0) {
      // If no extracted medicines, provide one empty medicine form
      const today = new Date().toISOString().split('T')[0];
      const oneMonthLater = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      return [{
        name: '',
        dosage: '',
        frequency: 'Once daily',
        duration: '30',
        instructions: 'Take as directed by your doctor',
        startDate: today,
        endDate: oneMonthLater,
      }];
    }

    // Map extracted medicines to form structure
    return extractedMedicines.map(name => {
      const today = new Date().toISOString().split('T')[0];
      const oneMonthLater = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      return {
        name: name.trim(),
        dosage: '',
        frequency: 'Once daily',
        duration: '30',
        instructions: 'Take as directed by your doctor',
        startDate: today,
        endDate: oneMonthLater,
      };
    });
  };

  const { control, register, handleSubmit, watch, setValue, reset, formState: { errors } } = useForm<{
    medicines: ExtractedMedicine[];
  }>({
    defaultValues: {
      medicines: initializeMedicines(),
    },
  });

  // Reset form when extractedMedicines change
  React.useEffect(() => {
    if (isOpen) {
      reset({
        medicines: initializeMedicines(),
      });
    }
  }, [isOpen, extractedMedicines, reset]);

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'medicines',
  });

  const watchedMedicines = watch('medicines');

  // Calculate end date based on start date and duration
  const calculateEndDate = (startDate: string, duration: string): string => {
    if (!startDate || !duration || duration === 'custom') return startDate;
    
    const start = new Date(startDate);
    const durationDays = parseInt(duration, 10);
    const end = new Date(start.getTime() + durationDays * 24 * 60 * 60 * 1000);
    return end.toISOString().split('T')[0];
  };

  const onSubmit = async (data: { medicines: ExtractedMedicine[] }) => {
    setIsSubmitting(true);

    try {
      // Validate user is authenticated and has patient_id
      if (!user?.id) {
        toast.error('User not authenticated. Please log in again.');
        return;
      }

      // Create each medicine individually
      const createdMedicines = [];

      for (const medicine of data.medicines) {
        const medicineData = {
          name: medicine.name,
          dosage: medicine.dosage,
          frequency: medicine.frequency,
          duration: parseInt(medicine.duration, 10), // Convert to number as required by API
          instructions: medicine.instructions,
          start_date: medicine.startDate,
          end_date: medicine.endDate,
          prescription_id: prescriptionId,
          patient_id: user.id, // Add the missing patient_id
        };

        const createdMedicine = await MedicineService.createMedicine(medicineData);
        createdMedicines.push(createdMedicine);
      }

      toast.success(`Successfully created ${createdMedicines.length} medicine records!`);
      onSuccess?.();
      onClose();
      
    } catch (error) {
      console.error('Error creating medicines:', error);
      toast.error('Failed to create medicine records. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addCustomMedicine = () => {
    const today = new Date().toISOString().split('T')[0];
    const oneMonthLater = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    append({
      name: '',
      dosage: '',
      frequency: 'Once daily',
      duration: '30',
      instructions: 'Take as directed by your doctor',
      startDate: today,
      endDate: oneMonthLater,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Complete Prescription Details</span>
          </DialogTitle>
          <DialogDescription>
            Please review and complete the details for each extracted medicine. 
            You can modify the dosage, frequency, and duration as prescribed by your doctor.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <Pill className="h-5 w-5" />
                <span>Extracted Medicines ({fields.length})</span>
              </h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addCustomMedicine}
                className="flex items-center space-x-1"
              >
                <Plus className="h-4 w-4" />
                <span>Add Medicine</span>
              </Button>
            </div>

            {fields.map((field, index) => (
              <Card key={field.id} className="relative">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        Medicine {index + 1}
                      </Badge>
                    </CardTitle>
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => remove(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Medicine Name */}
                  <div className="space-y-2">
                    <Label htmlFor={`medicines.${index}.name`}>
                      Medicine Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      {...register(`medicines.${index}.name`, { 
                        required: 'Medicine name is required' 
                      })}
                      placeholder="Enter medicine name"
                      className={errors.medicines?.[index]?.name ? 'border-red-500' : ''}
                    />
                    {errors.medicines?.[index]?.name && (
                      <p className="text-sm text-red-500 flex items-center space-x-1">
                        <AlertCircle className="h-3 w-3" />
                        <span>{errors.medicines?.[index]?.name?.message}</span>
                      </p>
                    )}
                  </div>

                  {/* Dosage */}
                  <div className="space-y-2">
                    <Label htmlFor={`medicines.${index}.dosage`}>
                      Dosage <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      {...register(`medicines.${index}.dosage`, { 
                        required: 'Dosage is required' 
                      })}
                      placeholder="e.g., 500mg, 1 tablet, 5ml"
                      className={errors.medicines?.[index]?.dosage ? 'border-red-500' : ''}
                    />
                    {errors.medicines?.[index]?.dosage && (
                      <p className="text-sm text-red-500 flex items-center space-x-1">
                        <AlertCircle className="h-3 w-3" />
                        <span>{errors.medicines?.[index]?.dosage?.message}</span>
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Frequency */}
                    <div className="space-y-2">
                      <Label>
                        Frequency <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={watchedMedicines[index]?.frequency || 'Once daily'}
                        onValueChange={(value) => setValue(`medicines.${index}.frequency`, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          {FREQUENCY_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Duration */}
                    <div className="space-y-2">
                      <Label>
                        Duration <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={watchedMedicines[index]?.duration || '30'}
                        onValueChange={(value) => {
                          setValue(`medicines.${index}.duration`, value);
                          if (value !== 'custom') {
                            const endDate = calculateEndDate(
                              watchedMedicines[index]?.startDate || new Date().toISOString().split('T')[0],
                              value
                            );
                            setValue(`medicines.${index}.endDate`, endDate);
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select duration" />
                        </SelectTrigger>
                        <SelectContent>
                          {DURATION_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Start Date */}
                    <div className="space-y-2">
                      <Label htmlFor={`medicines.${index}.startDate`}>
                        Start Date <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        type="date"
                        {...register(`medicines.${index}.startDate`, { 
                          required: 'Start date is required',
                          onChange: (e) => {
                            const duration = watchedMedicines[index]?.duration;
                            if (duration && duration !== 'custom') {
                              const endDate = calculateEndDate(e.target.value, duration);
                              setValue(`medicines.${index}.endDate`, endDate);
                            }
                          }
                        })}
                        className={errors.medicines?.[index]?.startDate ? 'border-red-500' : ''}
                      />
                    </div>

                    {/* End Date */}
                    <div className="space-y-2">
                      <Label htmlFor={`medicines.${index}.endDate`}>
                        End Date <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        type="date"
                        {...register(`medicines.${index}.endDate`, { 
                          required: 'End date is required' 
                        })}
                        className={errors.medicines?.[index]?.endDate ? 'border-red-500' : ''}
                      />
                    </div>
                  </div>

                  {/* Instructions */}
                  <div className="space-y-2">
                    <Label htmlFor={`medicines.${index}.instructions`}>
                      Instructions
                    </Label>
                    <Textarea
                      {...register(`medicines.${index}.instructions`)}
                      placeholder="Additional instructions from your doctor"
                      rows={2}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Separator />

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Creating Medicines...</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4" />
                  <span>Create {fields.length} Medicine{fields.length !== 1 ? 's' : ''}</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
