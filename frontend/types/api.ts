// Base API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  statusCode?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
  role: UserRole;
}

export interface AuthResponse {
  user: ApiUser;
  accessToken: string;
  refreshToken?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// User Types (matching backend)
export type UserRole = 'patient' | 'doctor' | 'hospital' | 'admin' | 'insurance';

export interface ApiUser {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile extends ApiUser {
  dateOfBirth?: string;
  phone?: string;
  address?: string;
  emergencyContact?: string;
  // Role-specific fields
  specialization?: string; // Doctor
  licenseNumber?: string; // Doctor
  hospitalId?: string; // Doctor
  companyName?: string; // Insurance
  website?: string; // Hospital/Insurance
}

// Medicine Types
export interface ApiMedicine {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  duration: number;
  instructions: string;
  side_effects?: string;
  start_date: string;
  end_date: string;
  prescription_id: string;
  patient_id: string;
  created_at: string;
  updated_at: string;
  adherence_records?: Array<{
    id: string;
    taken_time: string;
    status: 'taken' | 'missed' | 'skipped';
    scheduled_time: string;
  }>;
}

export interface CreateMedicineRequest {
  name: string;
  dosage: string;
  frequency: string;
  duration: number;
  instructions: string;
  side_effects?: string;
  start_date: string;
  end_date: string;
  prescription_id: string;
  patient_id: string;
}

// Prescription Types
export interface ApiPrescription {
  id: string;
  patientId: string;
  doctorId?: string;
  uploadedAt: string;
  filename: string;
  fileUrl: string;
  status: 'processing' | 'completed' | 'failed';
  extractedText?: string;
  confidence?: number;
  aiExtractedMedicines?: string[];
  aiExtractionSuccess?: boolean;
  medicines: ApiMedicine[];
  createdAt: string;
  updatedAt: string;
}

export interface CreatePrescriptionRequest {
  file: File;
  patientId?: string;
}

// Reminder Types
export interface ApiReminder {
  id: string;
  medicineId: string;
  patientId: string;
  scheduledTime: string;
  status: 'pending' | 'completed' | 'missed' | 'snoozed';
  reminderType: 'notification' | 'call' | 'sms';
  snoozeUntil?: string;
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateReminderRequest {
  medicineId: string;
  scheduledTime: string;
  reminderType: 'notification' | 'call' | 'sms';
}

export interface UpdateReminderRequest {
  status?: 'pending' | 'completed' | 'missed' | 'snoozed';
  snoozeUntil?: string;
  completedAt?: string;
}

// Adherence Types
export interface ApiAdherenceRecord {
  id: string;
  patientId: string;
  medicineId: string;
  scheduledTime: string;
  takenTime?: string;
  status: 'taken' | 'missed' | 'skipped';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAdherenceRecordRequest {
  medicineId: string;
  scheduledTime: string;
  takenTime?: string;
  status: 'taken' | 'missed' | 'skipped';
  notes?: string;
}

export interface AdherenceStats {
  totalReminders: number;
  takenCount: number;
  missedCount: number;
  skippedCount: number;
  adherenceRate: number;
  weeklyStats: Array<{
    date: string;
    taken: number;
    missed: number;
    skipped: number;
  }>;
}

// Gamification Types
export interface ApiAchievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'streak' | 'consistency' | 'completion' | 'milestone';
  requirement: number;
  points: number;
  createdAt: string;
  updatedAt: string;
}

export interface ApiUserAchievement {
  id: string;
  userId: string;
  achievementId: string;
  unlockedAt: string;
  achievement: ApiAchievement;
}

export interface ApiGamificationStats {
  userId: string;
  currentStreak: number;
  longestStreak: number;
  totalPoints: number;
  completionRate: number;
  level: number;
  achievements: ApiUserAchievement[];
  weeklyProgress: number[];
  monthlyProgress: number[];
  createdAt: string;
  updatedAt: string;
}

// Notification Types
export interface ApiNotification {
  id: string;
  userId: string;
  type: 'reminder' | 'achievement' | 'system' | 'adherence';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateNotificationRequest {
  userId: string;
  type: 'reminder' | 'achievement' | 'system' | 'adherence';
  title: string;
  message: string;
  data?: any;
}

// External Services Types
export interface TwilioTestRequest {
  to: string;
  message: string;
}

export interface ElevenLabsTestRequest {
  patientId: string;
  message: string;
}

export interface ExternalServiceResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Hospital-specific Types
export interface HospitalPatient extends ApiUser {
  dateOfBirth?: string;
  gender?: string;
  phone?: string;
  address?: string;
  emergencyContact?: string;
  assignedDoctorId?: string;
  department?: string;
  condition?: string;
  adherenceRate: number;
  lastVisit?: string;
  nextAppointment?: string;
  status: 'excellent' | 'good' | 'needs-attention' | 'critical';
  riskLevel: 'low' | 'medium' | 'high';
  medications: string[];
  notes?: string;
  insuranceProvider?: string;
  membershipId?: string;
  allergies?: string[];
}

export interface HospitalDoctor extends ApiUser {
  specialization: string;
  licenseNumber: string;
  experience: number;
  patients: number;
  adherenceRate: number;
  rating: number;
  reviews: number;
  status: 'active' | 'inactive' | 'on-leave';
  joinDate: string;
  schedule: string;
  department: string;
  qualifications: string[];
  languages: string[];
  consultationFee: number;
}

// Insurance-specific Types
export interface InsurancePolicyHolder extends ApiUser {
  policyNumber: string;
  policyType: string;
  coverageAmount: number;
  premium: number;
  startDate: string;
  endDate: string;
  status: 'active' | 'inactive' | 'suspended';
  adherenceRate: number;
  riskScore: number;
  claims: number;
  lastClaim?: string;
}

// Insurance API Types (from backend)
export interface PolicyHolder {
  patient_id: string;
  patient_name: string;
  patient_email: string;
  date_of_birth?: Date;
  emergency_contact?: string;
  assigned_doctor?: {
    id: string;
    name: string;
    specialization: string;
  };
  enrollment_date: Date;
  policy_type: string;
  coverage_area: string;
  active_medications_count: number;
  last_adherence_update: Date;
}

export interface InsuranceAdherenceReport {
  patient_id: string;
  patient_name: string;
  report_generated_at: Date;
  analysis_period: {
    start_date: Date;
    end_date: Date;
    days: number;
  };
  adherence_summary: {
    overall_adherence_rate: number;
    total_doses_prescribed: number;
    total_doses_taken: number;
    missed_doses: number;
    consistency_score: number;
  };
  risk_assessment: {
    risk_level: 'low' | 'medium' | 'high' | 'critical';
    risk_score: number;
    risk_factors: string[];
    recommendations: string[];
  };
  medication_details?: {
    medicine_id: string;
    medicine_name: string;
    dosage: string;
    frequency: string;
    adherence_rate: number;
    missed_doses: number;
    last_taken?: Date;
  }[];
  gamification_data?: {
    total_points: number;
    current_streak: number;
    longest_streak: number;
    completion_rate: number;
    level: string;
  };
  trends: {
    weekly_adherence: number[];
    improvement_trend: 'improving' | 'declining' | 'stable';
    consistency_score: number;
  };
}

export interface BulkAdherenceReport {
  insurance_provider_id: string;
  report_generated_at: Date;
  total_patients: number;
  summary: {
    average_adherence_rate: number;
    high_risk_patients: number;
    medium_risk_patients: number;
    low_risk_patients: number;
    total_medications_tracked: number;
  };
  patient_summaries: {
    patient_id: string;
    patient_name: string;
    adherence_rate: number;
    risk_level: 'low' | 'medium' | 'high' | 'critical';
    active_medications: number;
    last_update: Date;
  }[];
  detailed_reports?: InsuranceAdherenceReport[];
}

export interface RiskAssessmentReport {
  insurance_provider_id: string;
  assessment_date: Date;
  criteria: {
    min_adherence_rate: number;
    max_adherence_rate: number;
    analysis_period_days: number;
    risk_level_filter?: string;
  };
  high_risk_patients: {
    patient_id: string;
    patient_name: string;
    adherence_rate: number;
    risk_score: number;
    risk_factors: string[];
    recommended_actions: string[];
    contact_priority: 'immediate' | 'urgent' | 'routine';
  }[];
  statistics: {
    total_patients_assessed: number;
    critical_risk_count: number;
    high_risk_count: number;
    medium_risk_count: number;
    low_risk_count: number;
    average_adherence_rate: number;
    patients_needing_intervention: number;
  };
}

// Query Parameters
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface SearchParams extends PaginationParams {
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface DateRangeParams {
  startDate?: string;
  endDate?: string;
}

export interface AdherenceQueryParams extends SearchParams, DateRangeParams {
  patientId?: string;
  medicineId?: string;
  status?: 'taken' | 'missed' | 'skipped';
}

export interface ReminderQueryParams extends SearchParams, DateRangeParams {
  patientId?: string;
  medicineId?: string;
  status?: 'pending' | 'completed' | 'missed' | 'snoozed';
  reminderType?: 'notification' | 'call' | 'sms';
}
