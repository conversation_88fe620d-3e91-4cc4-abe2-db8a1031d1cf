export type UserRole = 'patient' | 'doctor' | 'hospital' | 'admin' | 'insurance';

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Medicine {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  duration: number; // in days
  instructions: string;
  sideEffects?: string;
  startDate: Date;
  endDate: Date;
  prescriptionId: string;
  lastTakenTime?: Date;
  adherenceRecords?: Array<{
    id: string;
    takenTime: Date;
    status: 'taken' | 'missed' | 'skipped';
    scheduledTime: Date;
  }>;
}

export interface Prescription {
  id: string;
  patientId: string;
  doctorId?: string;
  uploadedAt: Date;
  filename: string;
  fileUrl: string;
  status: 'processing' | 'completed' | 'failed';
  medicines: Medicine[];
  extractedText?: string;
  prescriptionDate?: Date | null;
  confidence?: number;
  aiExtractedMedicines?: string[];
  aiExtractionSuccess?: boolean;
}

export interface Reminder {
  id: string;
  medicineId: string;
  patientId: string;
  scheduledTime: Date;
  status: 'pending' | 'completed' | 'missed' | 'snoozed';
  reminderType: 'notification' | 'call' | 'sms';
  snoozeUntil?: Date;
  completedAt?: Date;
  medicine?: {
    id: string;
    name: string;
    dosage: string;
    frequency: string;
    instructions: string;
  };
}

export interface AdherenceRecord {
  id: string;
  patientId: string;
  medicineId: string;
  scheduledTime: Date;
  takenTime?: Date;
  status: 'taken' | 'missed' | 'skipped';
  notes?: string;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'streak' | 'consistency' | 'completion' | 'milestone';
  requirement: number;
  points: number;
}

export interface UserAchievement {
  id: string;
  userId: string;
  achievementId: string;
  unlockedAt: Date;
  achievement: Achievement;
}

export interface GamificationStats {
  currentStreak: number;
  longestStreak: number;
  totalPoints: number;
  completionRate: number;
  achievements: UserAchievement[];
  weeklyProgress: number[];
  monthlyProgress: number[];
  level?: {
    level: number;
    name: string;
    min_points: number;
    max_points: number;
    benefits: string[];
  };
  pointsToNextLevel?: number;
  nextMilestones?: Array<{
    type: 'streak' | 'points' | 'completion';
    target: number;
    current: number;
    progress_percentage: number;
    estimated_days: number;
  }>;
}

export interface Patient extends User {
  role: 'patient';
  dateOfBirth?: Date;
  emergencyContact?: string;
  assignedDoctorId?: string;
  gamificationStats: GamificationStats;
}

export interface Doctor extends User {
  role: 'doctor';
  specialization: string;
  licenseNumber: string;
  hospitalId?: string;
  patients: Patient[];
}

export interface Hospital extends User {
  role: 'hospital';
  address: string;
  phone: string;
  website?: string;
  doctors: Doctor[];
  patients: Patient[];
}

export interface Insurance extends User {
  role: 'insurance';
  companyName: string;
  address: string;
  phone: string;
  website?: string;
  policyTypes: string[];
  coverageAreas: string[];
  enrolledPatients: Patient[];
  partnerHospitals: Hospital[];
}

export interface Claim {
  id: string;
  patientId: string;
  insuranceId: string;
  hospitalId?: string;
  doctorId?: string;
  claimNumber: string;
  submissionDate: Date;
  serviceDate: Date;
  amount: number;
  status: 'pending' | 'approved' | 'rejected' | 'review';
  type: 'medication' | 'procedure' | 'consultation' | 'hospitalization' | 'other';
  description: string;
  documents: string[];
  notes?: string;
  approvedAmount?: number;
  approvalDate?: Date;
  rejectionReason?: string;
}

export interface Policy {
  id: string;
  insuranceId: string;
  patientId: string;
  policyNumber: string;
  policyType: string;
  startDate: Date;
  endDate: Date;
  coverageAmount: number;
  premium: number;
  status: 'active' | 'expired' | 'cancelled' | 'pending';
  benefits: string[];
  deductible: number;
  copay: number;
  medications: {
    covered: boolean;
    coveragePercentage: number;
    maxAmount?: number;
    excludedCategories?: string[];
  };
}