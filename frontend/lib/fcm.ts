// Firebase Cloud Messaging utilities for frontend
import { getMessaging, getToken, onMessage, MessagePayload } from 'firebase/messaging';
import { initializeApp, getApps } from 'firebase/app';

// Firebase configuration - replace with your actual config
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// VAPID key for web push
const VAPID_KEY = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY;

// Initialize Firebase app
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];

// FCM Service class
export class FCMService {
  private messaging: any = null;
  private isSupported = false;

  constructor() {
    this.initializeMessaging();
  }

  private async initializeMessaging() {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        console.log('FCM: Not in browser environment');
        return;
      }

      // Check if service workers are supported
      if (!('serviceWorker' in navigator)) {
        console.log('FCM: Service workers not supported');
        return;
      }

      // Check if push messaging is supported
      if (!('PushManager' in window)) {
        console.log('FCM: Push messaging not supported');
        return;
      }

      // Check if Notification API is supported
      if (!('Notification' in window)) {
        console.log('FCM: Notifications not supported');
        return;
      }

      // Initialize Firebase messaging
      this.messaging = getMessaging(app);
      this.isSupported = true;

      console.log('FCM: Messaging initialized successfully');
    } catch (error) {
      console.error('FCM: Failed to initialize messaging:', error);
    }
  }

  /**
   * Check if FCM is supported in the current environment
   */
  public isSupported(): boolean {
    return this.isSupported;
  }

  /**
   * Request notification permission from the user
   */
  public async requestPermission(): Promise<NotificationPermission> {
    if (!this.isSupported) {
      throw new Error('FCM not supported in this environment');
    }

    try {
      const permission = await Notification.requestPermission();
      console.log('FCM: Notification permission:', permission);
      return permission;
    } catch (error) {
      console.error('FCM: Failed to request permission:', error);
      throw error;
    }
  }

  /**
   * Get the current notification permission status
   */
  public getPermissionStatus(): NotificationPermission {
    if (!this.isSupported) {
      return 'denied';
    }
    return Notification.permission;
  }

  /**
   * Register service worker for background notifications
   */
  public async registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
    if (!this.isSupported) {
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
      console.log('FCM: Service worker registered:', registration);
      return registration;
    } catch (error) {
      console.error('FCM: Failed to register service worker:', error);
      return null;
    }
  }

  /**
   * Get FCM token for the current device
   */
  public async getToken(): Promise<string | null> {
    if (!this.isSupported || !this.messaging) {
      return null;
    }

    try {
      // Ensure service worker is registered
      await this.registerServiceWorker();

      // Request permission if not granted
      const permission = await this.requestPermission();
      if (permission !== 'granted') {
        console.log('FCM: Permission not granted');
        return null;
      }

      // Get FCM token
      const token = await getToken(this.messaging, {
        vapidKey: VAPID_KEY,
      });

      if (token) {
        console.log('FCM: Token generated:', token.substring(0, 20) + '...');
        return token;
      } else {
        console.log('FCM: No registration token available');
        return null;
      }
    } catch (error) {
      console.error('FCM: Failed to get token:', error);
      return null;
    }
  }

  /**
   * Store FCM token on the server
   */
  public async storeTokenOnServer(token: string): Promise<boolean> {
    try {
      const response = await fetch('/api/v1/notifications/fcm/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
        },
        body: JSON.stringify({
          token,
          deviceType: 'web',
          userAgent: navigator.userAgent,
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        console.log('FCM: Token stored on server successfully');
        return true;
      } else {
        console.error('FCM: Failed to store token on server:', result.message);
        return false;
      }
    } catch (error) {
      console.error('FCM: Error storing token on server:', error);
      return false;
    }
  }

  /**
   * Remove FCM token from the server
   */
  public async removeTokenFromServer(token: string): Promise<boolean> {
    try {
      const response = await fetch('/api/v1/notifications/fcm/token', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ token }),
      });

      const result = await response.json();
      
      if (result.success) {
        console.log('FCM: Token removed from server successfully');
        return true;
      } else {
        console.error('FCM: Failed to remove token from server:', result.message);
        return false;
      }
    } catch (error) {
      console.error('FCM: Error removing token from server:', error);
      return false;
    }
  }

  /**
   * Set up foreground message listener
   */
  public onMessage(callback: (payload: MessagePayload) => void): (() => void) | null {
    if (!this.isSupported || !this.messaging) {
      return null;
    }

    try {
      const unsubscribe = onMessage(this.messaging, (payload) => {
        console.log('FCM: Foreground message received:', payload);
        callback(payload);
      });

      return unsubscribe;
    } catch (error) {
      console.error('FCM: Failed to set up message listener:', error);
      return null;
    }
  }

  /**
   * Initialize FCM for the current user
   */
  public async initialize(): Promise<string | null> {
    if (!this.isSupported) {
      console.log('FCM: Not supported, skipping initialization');
      return null;
    }

    try {
      // Get FCM token
      const token = await this.getToken();
      
      if (token) {
        // Store token on server
        await this.storeTokenOnServer(token);
        
        // Set up foreground message listener
        this.onMessage((payload) => {
          // Handle foreground notifications
          this.handleForegroundNotification(payload);
        });

        return token;
      }

      return null;
    } catch (error) {
      console.error('FCM: Initialization failed:', error);
      return null;
    }
  }

  /**
   * Handle foreground notifications
   */
  private handleForegroundNotification(payload: MessagePayload) {
    const { notification, data } = payload;
    
    if (notification) {
      // Show browser notification for foreground messages
      if (Notification.permission === 'granted') {
        const notificationOptions: NotificationOptions = {
          body: notification.body,
          icon: notification.icon || '/icons/icon-192x192.png',
          badge: '/icons/badge-72x72.png',
          data: data,
          tag: data?.type || 'general',
        };

        new Notification(notification.title || 'MedCare', notificationOptions);
      }
    }
  }

  /**
   * Test push notification
   */
  public async testNotification(): Promise<boolean> {
    try {
      const response = await fetch('/api/v1/notifications/test/push-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          userId: 'current-user-id', // This should be replaced with actual user ID
          title: 'Test Push Notification',
          message: 'This is a test push notification from MedCare',
        }),
      });

      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('FCM: Test notification failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const fcmService = new FCMService();
