import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient } from '@/lib/api-client';
import { authService } from '@/lib/api/auth';
import { medicinesService } from '@/lib/api/medicines';
import { gamificationService } from '@/lib/api/gamification';
import { remindersService } from '@/lib/api/reminders';
import { adherenceService } from '@/lib/api/adherence';
import type {
  User,
  Medicine,
  Prescription,
  Reminder,
  AdherenceRecord,
  GamificationStats,
  UserRole
} from '@/types';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  hasHydrated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string, role: UserRole) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (updates: Partial<User>) => void;
  checkAuth: () => Promise<void>;
  clearError: () => void;
  setHasHydrated: (hasHydrated: boolean) => void;
}

interface MedicineState {
  medicines: Medicine[];
  prescriptions: Prescription[];
  reminders: Reminder[];
  adherenceRecords: AdherenceRecord[];
  isLoading: boolean;
  error: string | null;

  // API methods
  fetchMedicines: () => Promise<void>;
  fetchPrescriptions: () => Promise<void>;
  fetchReminders: () => Promise<void>;
  fetchTodaysReminders: () => Promise<void>;
  fetchUpcomingReminders: () => Promise<void>;
  fetchAdherenceRecords: () => Promise<void>;

  // Local methods
  addMedicine: (medicine: Medicine) => void;
  updateMedicine: (id: string, updates: Partial<Medicine>) => void;
  deleteMedicine: (id: string) => void;
  addPrescription: (prescription: Prescription) => void;
  updatePrescription: (id: string, updates: Partial<Prescription>) => void;
  addReminder: (reminder: Reminder) => void;
  updateReminder: (id: string, updates: Partial<Reminder>) => void;
  recordAdherence: (record: AdherenceRecord) => void;

  // Computed methods
  getTodaysReminders: () => Reminder[];
  getUpcomingReminders: () => Reminder[];
  getAdherenceRate: (days: number) => number;
  clearError: () => void;
}

interface GamificationState {
  stats: GamificationStats;
  isLoading: boolean;
  error: string | null;

  // API methods
  fetchStats: () => Promise<void>;

  // Local methods
  updateStats: (updates: Partial<GamificationStats>) => void;
  addPoints: (points: number) => void;
  incrementStreak: () => void;
  resetStreak: () => void;
  updateProgress: (taken: boolean) => void;
  clearError: () => void;
}

interface AppState {
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;
  notifications: Array<{
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    timestamp: Date;
    read: boolean;
  }>;
  toggleTheme: () => void;
  toggleSidebar: () => void;
  addNotification: (notification: Omit<AppState['notifications'][0], 'id' | 'timestamp'>) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      hasHydrated: false,

      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authService.login({ email, password });
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false
          });

          // Redirect based on user role using Next.js router instead of window.location
          if (typeof window !== 'undefined') {
            const userRole = response.user.role;
            // Use a small delay to ensure state is persisted
            setTimeout(() => {
              window.location.href = `/dashboard/${userRole}`;
            }, 100);
          }
        } catch (error: any) {
          set({
            error: error.message || 'Login failed',
            isLoading: false
          });
          throw error;
        }
      },

      register: async (name: string, email: string, password: string, role: UserRole) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authService.register({ name, email, password, role });
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false
          });

          // Redirect based on user role
          if (typeof window !== 'undefined') {
            const userRole = response.user.role;
            // Use a small delay to ensure state is persisted
            setTimeout(() => {
              window.location.href = `/dashboard/${userRole}`;
            }, 100);
          }
        } catch (error: any) {
          set({
            error: error.message || 'Registration failed',
            isLoading: false
          });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });
        try {
          await authService.logout();
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });
        } catch (error: any) {
          set({
            error: error.message || 'Logout failed',
            isLoading: false
          });
        }
      },

      checkAuth: async () => {
        set({ isLoading: true });
        try {
          const user = await authService.getCurrentUser();
          set({
            user,
            isAuthenticated: true,
            isLoading: false
          });
        } catch (error) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false
          });
        }
      },

      updateUser: (updates) => set((state) => ({
        user: state.user ? { ...state.user, ...updates } : null,
      })),

      clearError: () => set({ error: null }),

      setHasHydrated: (hasHydrated: boolean) => set({ hasHydrated }),
    }),
    {
      name: 'auth-storage',
      onRehydrateStorage: () => (state) => {
        state?.setHasHydrated(true);
      },
    }
  )
);

export const useMedicineStore = create<MedicineState>()(
  persist(
    (set, get) => ({
      medicines: [],
      prescriptions: [],
      reminders: [],
      adherenceRecords: [],
      isLoading: false,
      error: null,

      // API methods
      fetchMedicines: async () => {
        set({ isLoading: true, error: null });
        try {
          const response = await medicinesService.getMedicines();

          // Handle both array response and paginated response
          const medicinesData = Array.isArray(response) ? response : (response.data || []);

          // Transform API medicines to store format
          const transformedMedicines = medicinesData.map((apiMedicine: any) => {
            // Helper function to safely parse dates
            const parseDate = (dateValue: any): Date | null => {
              if (!dateValue || dateValue === null || dateValue === undefined) {
                return null;
              }

              try {
                const parsed = new Date(dateValue);
                return !isNaN(parsed.getTime()) ? parsed : null;
              } catch (error) {
                return null;
              }
            };

            // Transform ALL adherence records (not just taken ones)
            const adherenceRecords = (apiMedicine.adherenceRecords || apiMedicine.adherence_records || [])
              .map((record: any) => ({
                id: record.id,
                takenTime: parseDate(record.taken_time || record.takenTime) || new Date(),
                status: record.status,
                scheduledTime: parseDate(record.scheduled_time || record.scheduledTime) || new Date(),
              }));

            // Calculate last taken time from only the 'taken' records
            let lastTakenTime: Date | undefined;
            const takenRecords = adherenceRecords.filter(record => record.status === 'taken');

            if (takenRecords.length > 0) {
              // Find the most recent taken time
              lastTakenTime = takenRecords.reduce((latest: Date, record: any) => {
                return record.takenTime > latest ? record.takenTime : latest;
              }, takenRecords[0].takenTime);
            }

            return {
              id: apiMedicine.id,
              name: apiMedicine.name,
              dosage: apiMedicine.dosage,
              frequency: apiMedicine.frequency,
              duration: apiMedicine.duration,
              instructions: apiMedicine.instructions,
              sideEffects: apiMedicine.side_effects || apiMedicine.sideEffects,
              startDate: parseDate(apiMedicine.startDate || apiMedicine.start_date) || new Date(),
              endDate: parseDate(apiMedicine.endDate || apiMedicine.end_date) || new Date(),
              prescriptionId: apiMedicine.prescriptionId || apiMedicine.prescription_id,
              lastTakenTime,
              adherenceRecords,
            };
          });

          set({ medicines: transformedMedicines, isLoading: false });
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch medicines', isLoading: false });
        }
      },

      fetchPrescriptions: async () => {
        set({ isLoading: true, error: null });
        try {
          const { PrescriptionService } = await import('@/lib/api/prescriptions');
          const response = await PrescriptionService.getPrescriptions();

          // Handle both array response and paginated response
          const prescriptionsData = Array.isArray(response) ? response : (response.data || []);

          // Transform API prescriptions to store format
          const transformedPrescriptions = prescriptionsData.map((apiPrescription: any) => {
            // Helper function to safely parse dates
            const parseDate = (dateValue: any): Date | null => {
              if (!dateValue || dateValue === null || dateValue === undefined) {
                return null;
              }

              try {
                const parsed = new Date(dateValue);
                return !isNaN(parsed.getTime()) ? parsed : null;
              } catch (error) {
                return null;
              }
            };

            return {
              id: apiPrescription.id,
              patientId: apiPrescription.patientId,
              doctorId: apiPrescription.doctorId,
              uploadedAt: parseDate(apiPrescription.createdAt) || new Date(),
              filename: apiPrescription.filename,
              fileUrl: apiPrescription.fileUrl,
              status: apiPrescription.status,
              extractedText: apiPrescription.extractedText,
              medicines: apiPrescription.medicines || [],
              prescriptionDate: parseDate(apiPrescription.prescriptionDate),
              confidence: apiPrescription.textractConfidence,
              aiExtractedMedicines: apiPrescription.aiExtractedMedicines || [],
              aiExtractionSuccess: apiPrescription.aiExtractionSuccess || false,
            };
          });

          set({ prescriptions: transformedPrescriptions, isLoading: false });
        } catch (error: any) {
          console.error('Failed to fetch prescriptions:', error);
          set({ error: error.message || 'Failed to fetch prescriptions', isLoading: false });
        }
      },

      fetchReminders: async () => {
        set({ isLoading: true, error: null });
        try {
          // Get current user from auth store
          const authStore = useAuthStore.getState();
          if (!authStore.user) {
            throw new Error('User not authenticated');
          }

          // For patients, get their own reminders; for other roles, this would need patient ID parameter
          const response = await remindersService.getReminders();

          // Transform backend data to frontend format
          const reminders = (response.data || []).map((reminder: any) => ({
            id: reminder.id,
            medicineId: reminder.medicine_id,
            patientId: reminder.patient_id,
            scheduledTime: new Date(reminder.scheduled_time),
            status: reminder.status === 'sent' ? 'completed' : reminder.status, // Map 'sent' to 'completed'
            reminderType: reminder.reminder_type === 'both' ? 'notification' : reminder.reminder_type,
            snoozeUntil: reminder.snooze_until ? new Date(reminder.snooze_until) : undefined,
            completedAt: reminder.completed_at ? new Date(reminder.completed_at) : undefined,
          }));

          set({ reminders, isLoading: false });
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch reminders', isLoading: false });
        }
      },

      fetchTodaysReminders: async () => {
        set({ isLoading: true, error: null });
        try {
          const todaysReminders = await remindersService.getTodaysReminders();

          // Transform backend data to frontend format
          const reminders = todaysReminders.map((reminder: any) => ({
            id: reminder.id,
            medicineId: reminder.medicine_id,
            patientId: reminder.patient_id,
            scheduledTime: new Date(reminder.scheduled_time),
            status: reminder.status === 'sent' ? 'completed' : reminder.status,
            reminderType: reminder.reminder_type === 'both' ? 'notification' : reminder.reminder_type,
            snoozeUntil: reminder.snooze_until ? new Date(reminder.snooze_until) : undefined,
            completedAt: reminder.completed_at ? new Date(reminder.completed_at) : undefined,
          }));

          // Update only today's reminders in the store
          set((state) => ({
            reminders: [
              ...state.reminders.filter(r => {
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                const tomorrow = new Date(today);
                tomorrow.setDate(tomorrow.getDate() + 1);
                return !(r.scheduledTime >= today && r.scheduledTime < tomorrow);
              }),
              ...reminders
            ],
            isLoading: false
          }));
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch today\'s reminders', isLoading: false });
        }
      },

      fetchUpcomingReminders: async () => {
        set({ isLoading: true, error: null });
        try {
          const upcomingReminders = await remindersService.getUpcomingReminders(24);

          // Transform backend data to frontend format
          const reminders = upcomingReminders.map((reminder: any) => ({
            id: reminder.id,
            medicineId: reminder.medicine_id,
            patientId: reminder.patient_id,
            scheduledTime: new Date(reminder.scheduled_time),
            status: reminder.status === 'sent' ? 'completed' : reminder.status,
            reminderType: reminder.reminder_type === 'both' ? 'notification' : reminder.reminder_type,
            snoozeUntil: reminder.snooze_until ? new Date(reminder.snooze_until) : undefined,
            completedAt: reminder.completed_at ? new Date(reminder.completed_at) : undefined,
          }));

          // Update upcoming reminders in the store
          set((state) => ({
            reminders: [
              ...state.reminders.filter(r => {
                const now = new Date();
                const futureTime = new Date(now.getTime() + 24 * 60 * 60 * 1000);
                return !(r.scheduledTime > now && r.scheduledTime <= futureTime && r.status === 'pending');
              }),
              ...reminders
            ],
            isLoading: false
          }));
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch upcoming reminders', isLoading: false });
        }
      },

      fetchAdherenceRecords: async () => {
        set({ isLoading: true, error: null });
        try {
          const response = await adherenceService.getAdherenceRecords();
          const adherenceRecords = response.data || [];
          set({ adherenceRecords, isLoading: false });
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch adherence records', isLoading: false });
        }
      },

      // Local methods
      addMedicine: (medicine) => set((state) => ({
        medicines: [...state.medicines, medicine],
      })),

      updateMedicine: (id, updates) => set((state) => ({
        medicines: state.medicines.map((m) =>
          m.id === id ? { ...m, ...updates } : m
        ),
      })),

      deleteMedicine: (id) => set((state) => ({
        medicines: state.medicines.filter((m) => m.id !== id),
      })),
      
      addPrescription: (prescription) => set((state) => ({
        prescriptions: [...state.prescriptions, prescription],
      })),
      
      updatePrescription: (id, updates) => set((state) => ({
        prescriptions: state.prescriptions.map((p) => 
          p.id === id ? { ...p, ...updates } : p
        ),
      })),
      
      addReminder: (reminder) => set((state) => ({
        reminders: [...state.reminders, reminder],
      })),
      
      updateReminder: (id, updates) => set((state) => ({
        reminders: state.reminders.map((r) => 
          r.id === id ? { ...r, ...updates } : r
        ),
      })),
      
      recordAdherence: (record) => set((state) => ({
        adherenceRecords: [...state.adherenceRecords, record],
      })),
      
      getTodaysReminders: () => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        return get().reminders.filter((reminder) =>
          reminder.scheduledTime >= today && reminder.scheduledTime < tomorrow
        );
      },

      getUpcomingReminders: () => {
        const now = new Date();
        return get().reminders
          .filter((reminder) => reminder.scheduledTime > now && reminder.status === 'pending')
          .sort((a, b) => a.scheduledTime.getTime() - b.scheduledTime.getTime())
          .slice(0, 5);
      },
      
      getAdherenceRate: (days) => {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        const records = get().adherenceRecords.filter((record) =>
          record.scheduledTime >= cutoffDate
        );

        if (records.length === 0) return 0;

        const takenCount = records.filter((record) => record.status === 'taken').length;
        return Math.round((takenCount / records.length) * 100);
      },

      clearError: () => set({ error: null }),
    }),
    {
      name: 'medicine-storage',
    }
  )
);

export const useGamificationStore = create<GamificationState>()(
  persist(
    (set, get) => ({
      stats: {
        currentStreak: 0,
        longestStreak: 0,
        totalPoints: 0,
        completionRate: 0,
        achievements: [],
        weeklyProgress: Array(7).fill(0),
        monthlyProgress: Array(30).fill(0),
      },
      isLoading: false,
      error: null,

      // API methods
      fetchStats: async () => {
        set({ isLoading: true, error: null });
        try {
          // Get current user from auth store
          const authStore = useAuthStore.getState();
          if (!authStore.user) {
            throw new Error('User not authenticated');
          }

          // For patients, use their own ID; for other roles, this would need patient ID parameter
          const patientId = authStore.user.role === 'patient' ? authStore.user.id : authStore.user.id;

          // Call the dashboard endpoint which provides comprehensive gamification data
          const dashboardData = await gamificationService.getGamificationDashboard(patientId);

          // Transform backend data to frontend format
          const stats = {
            currentStreak: dashboardData.current_stats.current_streak,
            longestStreak: dashboardData.current_stats.longest_streak,
            totalPoints: dashboardData.current_stats.total_points,
            completionRate: dashboardData.current_stats.completion_rate,
            achievements: dashboardData.recent_achievements.map(achievement => ({
              id: achievement.achievement_id,
              name: achievement.achievement_name,
              points: achievement.points_earned,
              unlockedAt: new Date(achievement.unlocked_at),
            })),
            weeklyProgress: dashboardData.weekly_progress.current_week,
            monthlyProgress: dashboardData.monthly_progress.current_month,
            level: dashboardData.current_stats.level,
            pointsToNextLevel: dashboardData.current_stats.points_to_next_level,
            nextMilestones: dashboardData.next_milestones,
          };

          set({ stats, isLoading: false });
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch gamification stats', isLoading: false });
        }
      },

      // Local methods
      updateStats: (updates) => set((state) => ({
        stats: { ...state.stats, ...updates },
      })),
      
      addPoints: (points) => set((state) => ({
        stats: { ...state.stats, totalPoints: state.stats.totalPoints + points },
      })),
      
      incrementStreak: () => set((state) => ({
        stats: {
          ...state.stats,
          currentStreak: state.stats.currentStreak + 1,
          longestStreak: Math.max(state.stats.longestStreak, state.stats.currentStreak + 1),
        },
      })),
      
      resetStreak: () => set((state) => ({
        stats: { ...state.stats, currentStreak: 0 },
      })),
      
      updateProgress: (taken) => set((state) => {
        const today = new Date().getDay();
        const newWeeklyProgress = [...state.stats.weeklyProgress];
        newWeeklyProgress[today] = taken ? 100 : 0;

        return {
          stats: {
            ...state.stats,
            weeklyProgress: newWeeklyProgress,
          },
        };
      }),

      clearError: () => set({ error: null }),
    }),
    {
      name: 'gamification-storage',
    }
  )
);

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      theme: 'light',
      sidebarCollapsed: false,
      notifications: [],

      toggleTheme: () => set((state) => ({
        theme: state.theme === 'light' ? 'dark' : 'light',
      })),

      toggleSidebar: () => set((state) => ({
        sidebarCollapsed: !state.sidebarCollapsed,
      })),

      addNotification: (notification) => set((state) => ({
        notifications: [
          {
            ...notification,
            id: Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            read: false,
          },
          ...state.notifications,
        ],
      })),

      markNotificationRead: (id) => set((state) => ({
        notifications: state.notifications.map((n) =>
          n.id === id ? { ...n, read: true } : n
        ),
      })),

      clearNotifications: () => set({ notifications: [] }),
    }),
    {
      name: 'app-storage',
      storage: {
        getItem: (name) => {
          const str = localStorage.getItem(name);
          if (!str) return null;
          
          try {
            const parsed = JSON.parse(str);
            // Convert timestamp strings back to Date objects
            if (parsed.state?.notifications) {
              parsed.state.notifications = parsed.state.notifications.map((notification: any) => ({
                ...notification,
                timestamp: new Date(notification.timestamp),
              }));
            }
            return parsed;
          } catch (error) {
            console.error('Error parsing stored data:', error);
            return null;
          }
        },
        setItem: (name, value) => {
          localStorage.setItem(name, JSON.stringify(value));
        },
        removeItem: (name) => {
          localStorage.removeItem(name);
        },
      },
    }
  )
);

// Export doctor store
export { useDoctorStore } from './stores/doctor-store';

// Export hospital store
export { useHospitalStore } from './stores/hospital-store';