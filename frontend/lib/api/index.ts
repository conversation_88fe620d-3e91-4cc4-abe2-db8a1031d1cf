// Export all API services
import { AuthService } from './auth';
import { MedicineService } from './medicines';
import { PrescriptionService } from './prescriptions';
import { ReminderService } from './reminders';
import { AdherenceService } from './adherence';
import { GamificationService } from './gamification';
import { HospitalService } from './hospitals';
import { InsuranceService } from './insurance';

export { AuthService, MedicineService, PrescriptionService, ReminderService, AdherenceService, GamificationService, HospitalService, InsuranceService };

// Export API client and utilities
export { apiClient, TokenManager } from '../api-client';
export type { RequestConfig, ApiClientError } from '../api-client';

// Export all types
export * from '../../types/api';

// Convenience exports for commonly used services
export const api = {
  auth: AuthService,
  medicines: MedicineService,
  prescriptions: PrescriptionService,
  reminders: ReminderService,
  adherence: AdherenceService,
  gamification: GamificationService,
  hospitals: HospitalService,
  insurance: InsuranceService,
} as const;

// Default export
export default api;
