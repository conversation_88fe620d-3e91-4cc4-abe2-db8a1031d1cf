import { apiClient, ApiClientError } from '../api-client';

// Insurance API Types
export interface PolicyHolder {
  patient_id: string;
  patient_name: string;
  patient_email: string;
  date_of_birth?: Date;
  emergency_contact?: string;
  assigned_doctor?: {
    id: string;
    name: string;
    specialization: string;
  };
  enrollment_date: Date;
  policy_type: string;
  coverage_area: string;
  active_medications_count: number;
  last_adherence_update: Date;
}

export interface InsuranceAdherenceReport {
  patient_id: string;
  patient_name: string;
  report_generated_at: Date;
  analysis_period: {
    start_date: Date;
    end_date: Date;
    days: number;
  };
  adherence_summary: {
    overall_adherence_rate: number;
    total_doses_prescribed: number;
    total_doses_taken: number;
    missed_doses: number;
    consistency_score: number;
  };
  risk_assessment: {
    risk_level: 'low' | 'medium' | 'high' | 'critical';
    risk_score: number;
    risk_factors: string[];
    recommendations: string[];
  };
  medication_details?: {
    medicine_id: string;
    medicine_name: string;
    dosage: string;
    frequency: string;
    adherence_rate: number;
    missed_doses: number;
    last_taken?: Date;
  }[];
  gamification_data?: {
    total_points: number;
    current_streak: number;
    longest_streak: number;
    completion_rate: number;
    level: string;
  };
  trends: {
    weekly_adherence: number[];
    improvement_trend: 'improving' | 'declining' | 'stable';
    consistency_score: number;
  };
}

export interface BulkAdherenceReport {
  insurance_provider_id: string;
  report_generated_at: Date;
  total_patients: number;
  summary: {
    average_adherence_rate: number;
    high_risk_patients: number;
    medium_risk_patients: number;
    low_risk_patients: number;
    total_medications_tracked: number;
  };
  patient_summaries: {
    patient_id: string;
    patient_name: string;
    adherence_rate: number;
    risk_level: 'low' | 'medium' | 'high' | 'critical';
    active_medications: number;
    last_update: Date;
  }[];
  detailed_reports?: InsuranceAdherenceReport[];
}

export interface RiskAssessmentReport {
  insurance_provider_id: string;
  assessment_date: Date;
  criteria: {
    min_adherence_rate: number;
    max_adherence_rate: number;
    analysis_period_days: number;
    risk_level_filter?: string;
  };
  high_risk_patients: {
    patient_id: string;
    patient_name: string;
    adherence_rate: number;
    risk_score: number;
    risk_factors: string[];
    recommended_actions: string[];
    contact_priority: 'immediate' | 'urgent' | 'routine';
  }[];
  statistics: {
    total_patients_assessed: number;
    critical_risk_count: number;
    high_risk_count: number;
    medium_risk_count: number;
    low_risk_count: number;
    average_adherence_rate: number;
    patients_needing_intervention: number;
  };
}

// Query DTOs
export interface PolicyHolderQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sort_by?: 'name' | 'enrollment_date' | 'adherence_rate';
  sort_order?: 'asc' | 'desc';
  policy_type?: string;
  coverage_area?: string;
  min_adherence_rate?: number;
  max_adherence_rate?: number;
}

export interface AdherenceReportQueryDto {
  days?: number;
  include_medicine_details?: boolean;
  include_gamification?: boolean;
}

export interface BulkAdherenceQueryDto {
  patient_ids: string[];
  days?: number;
  include_details?: boolean;
}

export interface RiskAssessmentQueryDto {
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
  min_adherence_rate?: number;
  max_adherence_rate?: number;
  days?: number;
}

// Insurance API Service
export class InsuranceService {
  /**
   * Get policy holders for the insurance provider
   */
  static async getPolicyHolders(query?: PolicyHolderQueryDto): Promise<{
    data: PolicyHolder[];
    total: number;
    page: number;
    limit: number;
  }> {
    return apiClient.get('/insurance/policy-holders', { params: query as Record<string, string | number | boolean> });
  }

  /**
   * Get detailed adherence report for a specific policy holder
   */
  static async getPatientAdherenceReport(
    patientId: string,
    query?: AdherenceReportQueryDto
  ): Promise<InsuranceAdherenceReport> {
    return apiClient.get(`/insurance/adherence-report/${patientId}`, { params: query as Record<string, string | number | boolean> });
  }

  /**
   * Get bulk adherence report for multiple policy holders
   */
  static async getBulkAdherenceReport(query: BulkAdherenceQueryDto): Promise<BulkAdherenceReport> {
    return apiClient.post('/insurance/bulk-adherence-report', query);
  }

  /**
   * Get risk assessment report
   */
  static async getRiskAssessmentReport(query?: RiskAssessmentQueryDto): Promise<RiskAssessmentReport> {
    return apiClient.get('/insurance/risk-assessment', { params: query as Record<string, string | number | boolean> });
  }

  /**
   * Get policy holder summary (quick overview)
   */
  static async getPolicyHolderSummary(patientId: string): Promise<any> {
    return apiClient.get(`/insurance/policy-holder/${patientId}/summary`);
  }

  /**
   * Get dashboard summary statistics
   */
  static async getDashboardSummary(): Promise<{
    total_policy_holders: number;
    average_adherence_rate: number;
    high_risk_patients: number;
    low_adherence_patients: number;
    total_claims_this_month: number;
    cost_savings_estimate: number;
    top_performing_hospitals: Array<{
      hospital_name: string;
      adherence_rate: number;
      patient_count: number;
    }>;
    recent_alerts: Array<{
      type: 'high_risk' | 'low_adherence' | 'missed_appointment';
      patient_name: string;
      message: string;
      timestamp: Date;
      priority: 'low' | 'medium' | 'high';
    }>;
  }> {
    return apiClient.get('/insurance/dashboard-summary');
  }

  /**
   * Get hospital performance data for insurance provider
   */
  static async getHospitalPerformance(): Promise<Array<{
    hospital_id: string;
    hospital_name: string;
    location: string;
    policy_holders: number;
    doctors: number;
    adherence_rate: number;
    risk_score: number;
    cost_efficiency: number;
  }>> {
    return apiClient.get('/insurance/hospital-performance');
  }

  /**
   * Get adherence analytics and trends
   */
  static async getAdherenceAnalytics(params?: {
    days?: number;
    group_by?: 'condition' | 'hospital' | 'doctor';
  }): Promise<{
    overall_metrics: {
      total_patients: number;
      average_adherence: number;
      improvement_rate: number;
      cost_impact: number;
    };
    condition_breakdown: Array<{
      condition: string;
      patient_count: number;
      adherence_rate: number;
      risk_level: string;
    }>;
    hospital_performance: Array<{
      hospital_name: string;
      adherence_rate: number;
      patient_count: number;
      cost_per_patient: number;
    }>;
    trends: {
      weekly_adherence: number[];
      monthly_costs: number[];
      risk_distribution: {
        low: number;
        medium: number;
        high: number;
        critical: number;
      };
    };
  }> {
    return apiClient.get('/insurance/adherence-analytics', { params });
  }
}
