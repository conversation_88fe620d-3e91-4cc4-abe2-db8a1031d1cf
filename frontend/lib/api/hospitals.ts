import { ApiResponse } from '../../types/api';
import { ApiClientError, apiClient } from '../api-client';

// Hospital API Types
export interface HospitalPatient {
  patient_id: string;
  patient_name: string;
  patient_email: string;
  date_of_birth?: Date;
  emergency_contact?: string;
  assigned_doctor: {
    id: string;
    name: string;
    specialization: string;
  };
  admission_date?: Date;
  current_medications_count: number;
  last_adherence_check: Date;
}

export interface HospitalAdherenceReport {
  patient_id: string;
  patient_name: string;
  hospital_id: string;
  report_date: Date;
  analysis_period: {
    start_date: Date;
    end_date: Date;
    days_analyzed: number;
  };
  overall_adherence: {
    adherence_rate: number;
    total_doses: number;
    taken_doses: number;
    missed_doses: number;
    current_streak: number;
    longest_streak: number;
  };
  medication_details?: {
    medicine_id: string;
    medicine_name: string;
    dosage: string;
    frequency: string;
    adherence_rate: number;
    missed_doses: number;
    last_taken?: Date;
  }[];
  weekly_trends: {
    week_start: Date;
    adherence_rate: number;
    improvement: number;
  }[];
  assigned_doctor: {
    id: string;
    name: string;
    specialization: string;
  };
  gamification_data?: {
    total_points: number;
    current_streak: number;
    longest_streak: number;
    completion_rate: number;
    level: string;
  };
  recommendations: string[];
  risk_assessment: {
    risk_level: 'low' | 'medium' | 'high' | 'critical';
    risk_factors: string[];
    intervention_needed: boolean;
  };
}

export interface HospitalBulkAdherenceReport {
  hospital_id: string;
  report_date: Date;
  analysis_period: {
    start_date: Date;
    end_date: Date;
    days_analyzed: number;
  };
  summary: {
    total_patients: number;
    average_adherence_rate: number;
    patients_above_threshold: number;
    patients_needing_intervention: number;
  };
  patient_summaries: {
    patient_id: string;
    patient_name: string;
    adherence_rate: number;
    risk_level: 'low' | 'medium' | 'high' | 'critical';
    assigned_doctor: string;
    department: string;
  }[];
  detailed_reports?: HospitalAdherenceReport[];
  department_breakdown: {
    department: string;
    patient_count: number;
    average_adherence: number;
    high_risk_patients: number;
  }[];
}

export interface HospitalDepartmentAnalysis {
  hospital_id: string;
  department: string;
  analysis_date: Date;
  analysis_period: {
    start_date: Date;
    end_date: Date;
    days_analyzed: number;
  };
  metrics: {
    total_patients: number;
    total_doctors: number;
    average_adherence_rate: number;
    patients_above_80_percent: number;
    patients_needing_intervention: number;
  };
  doctor_performance: {
    doctor_id: string;
    doctor_name: string;
    patient_count: number;
    average_adherence: number;
    high_risk_patients: number;
  }[];
  patient_risk_distribution: {
    low_risk: number;
    medium_risk: number;
    high_risk: number;
    critical_risk: number;
  };
  trends: {
    week_start: Date;
    adherence_rate: number;
    patient_count: number;
    improvement: number;
  }[];
  recommendations: string[];
}

export interface HospitalDashboardSummary {
  hospital_id: string;
  hospital_name: string;
  summary_date: Date;
  overview: {
    total_patients: number;
    total_doctors: number;
    total_departments: number;
    active_prescriptions: number;
  };
  adherence_metrics: {
    overall_adherence_rate: number;
    patients_above_80_percent: number;
    patients_needing_intervention: number;
    trend_direction: 'improving' | 'declining' | 'stable';
  };
  department_highlights: {
    best_performing: {
      department: string;
      adherence_rate: number;
    };
    needs_attention: {
      department: string;
      adherence_rate: number;
      patient_count: number;
    };
  };
  recent_alerts: {
    critical_patients: number;
    missed_appointments: number;
    medication_adherence_alerts: number;
  };
  weekly_trends: {
    week_start: Date;
    adherence_rate: number;
    patient_count: number;
    alerts_count: number;
  }[];
}

// Query interfaces
export interface HospitalPatientsQuery {
  page?: number;
  limit?: number;
  search?: string;
  department?: string;
  doctor_id?: string;
  adherence_threshold?: number;
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
}

export interface HospitalAdherenceReportQuery {
  days?: number;
  include_medications?: boolean;
  include_gamification?: boolean;
}

export interface HospitalBulkAdherenceQuery {
  patient_ids: string[];
  days?: number;
  include_detailed_reports?: boolean;
  group_by_department?: boolean;
}

export interface HospitalDepartmentAnalysisQuery {
  department: string;
  days?: number;
  include_doctor_breakdown?: boolean;
  include_trends?: boolean;
}

export interface HospitalDoctorsQuery {
  page?: number;
  limit?: number;
  search?: string;
  specialization?: string;
  status?: 'active' | 'inactive' | 'on-leave';
}

export interface HospitalDoctor {
  id: string;
  name: string;
  email: string;
  phone?: string;
  specialization: string;
  license_number: string;
  experience?: number;
  patients_count?: number;
  adherence_rate?: number;
  rating?: number;
  reviews_count?: number;
  status: 'active' | 'inactive' | 'on-leave';
  join_date: Date;
  schedule?: string;
  qualifications?: string[];
  languages?: string[];
  consultation_fee?: number;
  avatar?: string;
}

// Hospital API Service
export class HospitalService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const method = (options.method as any) || 'GET';
      const body = options.body ? JSON.parse(options.body as string) : undefined;

      const data = await apiClient.request<T>(endpoint, {
        method,
        body,
        requireAuth: true,
        showErrorToast: false, // We'll handle errors in the calling code
      });

      return { data };
    } catch (error) {
      if (error instanceof ApiClientError) {
        throw error;
      }
      throw new ApiClientError(
        error instanceof Error ? error.message : 'Network error',
        0
      );
    }
  }

  // Get hospital patients
  async getHospitalPatients(query: HospitalPatientsQuery = {}): Promise<{
    data: HospitalPatient[];
    total: number;
    page: number;
    limit: number;
  }> {
    const searchParams = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await this.makeRequest<{
      data: HospitalPatient[];
      total: number;
      page: number;
      limit: number;
    }>(`/hospital/patients?${searchParams.toString()}`);

    return response.data || { data: [], total: 0, page: 1, limit: 10 };
  }

  // Get hospital doctors
  async getHospitalDoctors(query: HospitalDoctorsQuery = {}): Promise<{
    data: HospitalDoctor[];
    total: number;
    page: number;
    limit: number;
  }> {
    // For now, we'll use the users endpoint to get doctors
    // In the future, this could be moved to a dedicated hospital/doctors endpoint
    const searchParams = new URLSearchParams();
    searchParams.append('role', 'doctor');

    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && key !== 'status') { // status filtering will be done client-side for now
        searchParams.append(key, value.toString());
      }
    });

    const response = await this.makeRequest<HospitalDoctor[]>(`/users?${searchParams.toString()}`);

    // Transform the response to match expected format
    const doctors = response.data || [];

    // Filter by status if provided (client-side filtering for now)
    const filteredDoctors = query.status
      ? doctors.filter(doctor => doctor.status === query.status)
      : doctors;

    return {
      data: filteredDoctors,
      total: filteredDoctors.length,
      page: query.page || 1,
      limit: query.limit || 10,
    };
  }

  // Get patient adherence report
  async getPatientAdherenceReport(
    patientId: string,
    query: HospitalAdherenceReportQuery = {}
  ): Promise<HospitalAdherenceReport> {
    const searchParams = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await this.makeRequest<HospitalAdherenceReport>(
      `/hospital/adherence-report/${patientId}?${searchParams.toString()}`
    );

    if (!response.data) {
      throw new Error('Failed to fetch adherence report');
    }

    return response.data;
  }

  // Get bulk adherence report
  async getBulkAdherenceReport(
    query: HospitalBulkAdherenceQuery
  ): Promise<HospitalBulkAdherenceReport> {
    const response = await this.makeRequest<HospitalBulkAdherenceReport>(
      '/hospital/bulk-adherence-report',
      {
        method: 'POST',
        body: JSON.stringify(query),
      }
    );

    if (!response.data) {
      throw new Error('Failed to fetch bulk adherence report');
    }

    return response.data;
  }

  // Get department analysis
  async getDepartmentAnalysis(
    query: HospitalDepartmentAnalysisQuery
  ): Promise<HospitalDepartmentAnalysis> {
    const searchParams = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await this.makeRequest<HospitalDepartmentAnalysis>(
      `/hospital/department-analysis?${searchParams.toString()}`
    );

    if (!response.data) {
      throw new Error('Failed to fetch department analysis');
    }

    return response.data;
  }

  // Get dashboard summary
  async getDashboardSummary(): Promise<HospitalDashboardSummary> {
    const response = await this.makeRequest<HospitalDashboardSummary>(
      '/hospital/dashboard-summary'
    );

    if (!response.data) {
      throw new Error('Failed to fetch dashboard summary');
    }

    return response.data;
  }

  // Get patient summary
  async getPatientSummary(patientId: string): Promise<any> {
    const response = await this.makeRequest<any>(
      `/hospital/patient/${patientId}/summary`
    );

    if (!response.data) {
      throw new Error('Failed to fetch patient summary');
    }

    return response.data;
  }
}

// Export singleton instance
export const hospitalService = new HospitalService();
