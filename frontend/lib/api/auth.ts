import { apiClient, TokenManager } from '../api-client';
import type {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  RefreshTokenRequest,
  ApiUser,
} from '../../types/api';

export class AuthService {
  /**
   * <PERSON>gin user with email and password
   */
  static async login(credentials: LoginRequest): Promise<AuthResponse> {
    console.log('🔐 AuthService.login called with:', { email: credentials.email });

    try {
      console.log('📡 Making login API request...');
      const response = await apiClient.post<AuthResponse>('/auth/login', credentials, {
        requireAuth: false,
        showSuccessToast: false, // We'll handle success toast manually
        showErrorToast: false,   // We'll handle error toast manually
      });

      console.log('✅ Login API response received:', {
        hasUser: !!response.user,
        hasAccessToken: !!response.accessToken,
        hasRefreshToken: !!response.refreshToken,
        userRole: response.user?.role,
        userId: response.user?.id
      });

      // Validate response structure
      if (!response.accessToken) {
        console.error('❌ Login response missing accessToken:', response);
        throw new Error('Invalid login response: missing access token');
      }

      if (!response.user) {
        console.error('❌ Login response missing user:', response);
        throw new Error('Invalid login response: missing user data');
      }

      // Store tokens with debugging
      console.log('💾 Storing access token...');
      TokenManager.setToken(response.accessToken);

      if (response.refreshToken) {
        console.log('💾 Storing refresh token...');
        TokenManager.setRefreshToken(response.refreshToken);
      }

      // Verify tokens were stored
      const storedToken = TokenManager.getToken();
      const storedRefreshToken = TokenManager.getRefreshToken();

      console.log('🔍 Token storage verification:', {
        tokenStored: !!storedToken,
        refreshTokenStored: !!storedRefreshToken,
        tokenLength: storedToken?.length,
        tokenExpired: storedToken ? TokenManager.isTokenExpired(storedToken) : 'no token'
      });

      if (!storedToken) {
        console.error('❌ Token was not stored in localStorage!');
        throw new Error('Failed to store authentication token');
      }

      console.log('✅ Login successful, tokens stored');
      return response;

    } catch (error: any) {
      console.error('❌ AuthService.login error:', {
        message: error.message,
        status: error.statusCode,
        error: error
      });

      // Re-throw with more context
      throw new Error(error.message || 'Login failed');
    }
  }

  /**
   * Register new user
   */
  static async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/register', userData, {
      requireAuth: false,
      showSuccessToast: true,
      successMessage: 'Registration successful!',
    });

    // Store tokens
    TokenManager.setToken(response.accessToken);
    if (response.refreshToken) {
      TokenManager.setRefreshToken(response.refreshToken);
    }

    return response;
  }

  /**
   * Logout user
   */
  static async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout', {}, {
        showSuccessToast: true,
        successMessage: 'Logged out successfully',
      });
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout API call failed:', error);
    } finally {
      // Always clear tokens
      TokenManager.clearTokens();
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(): Promise<AuthResponse> {
    const refreshToken = TokenManager.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await apiClient.post<AuthResponse>('/auth/refresh', {
      refreshToken,
    }, {
      requireAuth: false,
    });

    // Update stored tokens
    TokenManager.setToken(response.accessToken);
    if (response.refreshToken) {
      TokenManager.setRefreshToken(response.refreshToken);
    }

    return response;
  }

  /**
   * Get current user profile
   */
  static async getCurrentUser(): Promise<ApiUser> {
    return apiClient.get<ApiUser>('/auth/me');
  }

  /**
   * Update user profile
   */
  static async updateProfile(updates: Partial<ApiUser>): Promise<ApiUser> {
    return apiClient.patch<ApiUser>('/auth/profile', updates, {
      showSuccessToast: true,
      successMessage: 'Profile updated successfully',
    });
  }

  /**
   * Change password
   */
  static async changePassword(data: {
    currentPassword: string;
    newPassword: string;
  }): Promise<void> {
    await apiClient.post('/auth/change-password', data, {
      showSuccessToast: true,
      successMessage: 'Password changed successfully',
    });
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<void> {
    await apiClient.post('/auth/forgot-password', { email }, {
      requireAuth: false,
      showSuccessToast: true,
      successMessage: 'Password reset email sent',
    });
  }

  /**
   * Reset password with token
   */
  static async resetPassword(data: {
    token: string;
    newPassword: string;
  }): Promise<void> {
    await apiClient.post('/auth/reset-password', data, {
      requireAuth: false,
      showSuccessToast: true,
      successMessage: 'Password reset successfully',
    });
  }

  /**
   * Verify email address
   */
  static async verifyEmail(token: string): Promise<void> {
    await apiClient.post('/auth/verify-email', { token }, {
      requireAuth: false,
      showSuccessToast: true,
      successMessage: 'Email verified successfully',
    });
  }

  /**
   * Resend email verification
   */
  static async resendEmailVerification(): Promise<void> {
    await apiClient.post('/auth/resend-verification', {}, {
      showSuccessToast: true,
      successMessage: 'Verification email sent',
    });
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    const token = TokenManager.getToken();
    if (!token) return false;
    
    return !TokenManager.isTokenExpired(token);
  }

  /**
   * Get user role from token
   */
  static getUserRole(): string | null {
    const token = TokenManager.getToken();
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.role || null;
    } catch {
      return null;
    }
  }

  /**
   * Get user ID from token
   */
  static getUserId(): string | null {
    const token = TokenManager.getToken();
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.sub || payload.userId || null;
    } catch {
      return null;
    }
  }

  /**
   * Check if user has specific role
   */
  static hasRole(role: string): boolean {
    const userRole = this.getUserRole();
    return userRole === role;
  }

  /**
   * Check if user has any of the specified roles
   */
  static hasAnyRole(roles: string[]): boolean {
    const userRole = this.getUserRole();
    return userRole ? roles.includes(userRole) : false;
  }
}

// Export both the class and an instance
export const authService = AuthService;
export default AuthService;
