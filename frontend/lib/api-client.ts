import { toast } from 'sonner';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1';

// Types for API responses
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  statusCode?: number;
}

export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
  details?: any;
}

// Custom error class for API errors
export class ApiClientError extends Error {
  statusCode: number;
  details?: any;

  constructor(message: string, statusCode: number, details?: any) {
    super(message);
    this.name = 'ApiClientError';
    this.statusCode = statusCode;
    this.details = details;
  }
}

// Token management utilities
class TokenManager {
  private static readonly TOKEN_KEY = 'auth-token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh-token';

  static getToken(): string | null {
    console.log('🔍 TokenManager.getToken called:', {
      hasWindow: typeof window !== 'undefined',
      tokenKey: this.TOKEN_KEY
    });

    if (typeof window === 'undefined') {
      console.warn('⚠️ Cannot get token: window is undefined (SSR)');
      return null;
    }

    try {
      const token = localStorage.getItem(this.TOKEN_KEY);
      console.log('📖 Token retrieved from localStorage:', {
        hasToken: !!token,
        tokenLength: token?.length,
        tokenPreview: token?.substring(0, 20) + '...'
      });
      return token;
    } catch (error) {
      console.error('❌ Failed to get token from localStorage:', error);
      return null;
    }
  }

  static setToken(token: string): void {
    console.log('💾 TokenManager.setToken called:', {
      hasWindow: typeof window !== 'undefined',
      tokenLength: token?.length,
      tokenPreview: token?.substring(0, 20) + '...'
    });

    if (typeof window === 'undefined') {
      console.warn('⚠️ Cannot set token: window is undefined (SSR)');
      return;
    }

    // Test localStorage availability
    try {
      const testKey = 'test-storage';
      const testValue = 'test-value';
      localStorage.setItem(testKey, testValue);
      const retrieved = localStorage.getItem(testKey);
      localStorage.removeItem(testKey);

      if (retrieved !== testValue) {
        throw new Error('localStorage test failed');
      }
      console.log('✅ localStorage is working');
    } catch (error) {
      console.error('❌ localStorage is not available:', error);
      throw new Error('localStorage is not available');
    }

    try {
      console.log('📝 Setting token in localStorage with key:', this.TOKEN_KEY);
      localStorage.setItem(this.TOKEN_KEY, token);
      console.log('✅ Token stored in localStorage');

      // Verify storage immediately
      const stored = localStorage.getItem(this.TOKEN_KEY);
      console.log('🔍 Immediate verification:', {
        stored: !!stored,
        matches: stored === token,
        storedLength: stored?.length,
        expectedLength: token.length
      });

      if (stored === token) {
        console.log('✅ Token storage verified');
      } else {
        console.error('❌ Token storage verification failed!', {
          expected: token.substring(0, 20) + '...',
          actual: stored?.substring(0, 20) + '...'
        });
        throw new Error('Token storage verification failed');
      }

      // Test retrieval after a short delay
      setTimeout(() => {
        const delayedCheck = localStorage.getItem(this.TOKEN_KEY);
        console.log('⏰ Delayed token check:', {
          stillThere: !!delayedCheck,
          matches: delayedCheck === token
        });
      }, 100);

    } catch (error) {
      console.error('❌ Failed to store token in localStorage:', error);
      throw error;
    }
  }

  static getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setRefreshToken(token: string): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
  }

  static clearTokens(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }
}

// Request configuration interface
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string | number | boolean>;
  requireAuth?: boolean;
  showErrorToast?: boolean;
  showSuccessToast?: boolean;
  successMessage?: string;
}

// Main API client class
class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  // Build URL with query parameters
  private buildURL(endpoint: string, params?: Record<string, string | number | boolean>): string {
    const url = new URL(`${this.baseURL}${endpoint}`);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, String(value));
      });
    }

    return url.toString();
  }

  // Get authorization headers
  private getAuthHeaders(): Record<string, string> {
    const token = TokenManager.getToken();
    console.log('🔑 getAuthHeaders called:', {
      hasToken: !!token,
      tokenLength: token?.length,
      tokenPreview: token?.substring(0, 20) + '...'
    });

    if (!token) {
      console.warn('⚠️ No token found in localStorage for auth headers');
      return {};
    }

    return { Authorization: `Bearer ${token}` };
  }

  // Handle API response
  private async handleResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');
    
    let data: any;
    try {
      data = isJson ? await response.json() : await response.text();
    } catch (error) {
      throw new ApiClientError('Failed to parse response', response.status);
    }

    if (!response.ok) {
      const errorMessage = data?.message || data?.error || `HTTP ${response.status}`;
      throw new ApiClientError(errorMessage, response.status, data);
    }

    return data;
  }

  // Refresh token if needed
  private async refreshTokenIfNeeded(): Promise<void> {
    const token = TokenManager.getToken();
    const refreshToken = TokenManager.getRefreshToken();

    if (!token || !refreshToken) return;

    if (TokenManager.isTokenExpired(token)) {
      try {
        const response = await fetch(`${this.baseURL}/auth/refresh`, {
          method: 'POST',
          headers: this.defaultHeaders,
          body: JSON.stringify({ refreshToken }),
        });

        if (response.ok) {
          const data = await response.json();
          TokenManager.setToken(data.accessToken);
          if (data.refreshToken) {
            TokenManager.setRefreshToken(data.refreshToken);
          }
        } else {
          // Refresh failed, clear tokens and redirect to login
          TokenManager.clearTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
        }
      } catch (error) {
        console.error('Token refresh failed:', error);
        TokenManager.clearTokens();
      }
    }
  }

  // Main request method
  async request<T = any>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    const {
      method = 'GET',
      headers = {},
      body,
      params,
      requireAuth = true,
      showErrorToast = true,
      showSuccessToast = false,
      successMessage,
    } = config;

    console.log('🌐 ApiClient.request:', {
      method,
      endpoint,
      requireAuth,
      hasBody: !!body,
      hasParams: !!params
    });

    try {
      // Refresh token if needed and auth is required
      if (requireAuth) {
        await this.refreshTokenIfNeeded();
      }

      // Build headers
      const requestHeaders = {
        ...this.defaultHeaders,
        ...(requireAuth ? this.getAuthHeaders() : {}),
        ...headers,
      };

      // Build URL
      const url = this.buildURL(endpoint, params);

      console.log('📡 Making fetch request:', {
        url,
        method,
        hasAuthHeader: !!requestHeaders.Authorization,
        headers: Object.keys(requestHeaders)
      });

      // Prepare request body
      let requestBody: string | FormData | undefined;
      if (body) {
        if (body instanceof FormData) {
          requestBody = body;
          // Remove Content-Type header for FormData (browser will set it with boundary)
          delete requestHeaders['Content-Type'];
        } else {
          requestBody = JSON.stringify(body);
          console.log('📤 Request body:', body);
        }
      }

      // Make request
      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: requestBody,
      });

      console.log('📥 Fetch response:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        url: response.url
      });

      const data = await this.handleResponse<T>(response);

      console.log('✅ Request successful:', {
        endpoint,
        method,
        dataKeys: typeof data === 'object' && data ? Object.keys(data) : 'not object'
      });

      // Show success toast if configured
      if (showSuccessToast && successMessage) {
        toast.success(successMessage);
      }

      return data;
    } catch (error) {
      console.error('❌ ApiClient.request error:', {
        endpoint,
        method,
        error: error instanceof Error ? error.message : error
      });

      // Handle API errors
      if (error instanceof ApiClientError) {
        if (showErrorToast) {
          toast.error(error.message);
        }

        // Handle unauthorized errors
        if (error.statusCode === 401) {
          TokenManager.clearTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
        }

        throw error;
      }

      // Handle network errors
      const networkError = new ApiClientError(
        'Network error occurred. Please check your connection.',
        0
      );

      if (showErrorToast) {
        toast.error(networkError.message);
      }

      throw networkError;
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, config?: Omit<RequestConfig, 'method'>): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T = any>(endpoint: string, body?: any, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'POST', body });
  }

  async put<T = any>(endpoint: string, body?: any, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'PUT', body });
  }

  async patch<T = any>(endpoint: string, body?: any, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'PATCH', body });
  }

  async delete<T = any>(endpoint: string, config?: Omit<RequestConfig, 'method'>): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export token manager for external use
export { TokenManager };

// Export types
export type { RequestConfig };
