import { useEffect } from 'react';
import { useInsuranceStore } from '@/lib/stores/insurance-store';
import { PolicyHolderQueryDto, AdherenceReportQueryDto, RiskAssessmentQueryDto } from '@/lib/api/insurance';

// Hook for managing policy holders
export function usePolicyHolders(query?: PolicyHolderQueryDto) {
  const {
    policyHolders,
    policyHoldersTotal,
    policyHoldersPage,
    policyHoldersLimit,
    policyHoldersLoading,
    policyHoldersError,
    fetchPolicyHolders,
  } = useInsuranceStore();

  const refreshPolicyHolders = () => fetchPolicyHolders(query);

  return {
    policyHolders: policyHolders || [],
    total: policyHoldersTotal || 0,
    page: policyHoldersPage || 1,
    limit: policyHoldersLimit || 20,
    isLoading: policyHoldersLoading || false,
    error: policyHoldersError || null,
    hasError: !!policyHoldersError,
    fetchPolicyHolders: (newQuery?: PolicyHolderQueryDto) => fetchPolicyHolders ? fetchPolicyHolders(newQuery || query) : Promise.resolve(),
    refreshPolicyHolders,
  };
}

// Hook for managing dashboard summary
export function useInsuranceDashboard() {
  const {
    dashboardSummary,
    dashboardLoading,
    dashboardError,
    fetchDashboardSummary,
  } = useInsuranceStore();

  const refreshDashboard = () => fetchDashboardSummary();

  return {
    dashboardSummary: dashboardSummary || null,
    isLoading: dashboardLoading || false,
    error: dashboardError || null,
    hasError: !!dashboardError,
    fetchDashboardSummary: fetchDashboardSummary || (() => {}),
    refreshDashboard,
  };
}

// Hook for managing adherence reports
export function useAdherenceReports() {
  const {
    adherenceReports,
    adherenceReportsLoading,
    adherenceReportsError,
    fetchPatientAdherenceReport,
  } = useInsuranceStore();

  return {
    adherenceReports,
    adherenceReportsLoading,
    adherenceReportsError,
    fetchPatientAdherenceReport,
    getPatientReport: (patientId: string) => adherenceReports[patientId],
    isPatientReportLoading: (patientId: string) => adherenceReportsLoading[patientId] || false,
    getPatientReportError: (patientId: string) => adherenceReportsError[patientId],
  };
}

// Hook for managing bulk reports
export function useBulkAdherenceReport() {
  const {
    bulkAdherenceReport,
    bulkReportLoading,
    bulkReportError,
    fetchBulkAdherenceReport,
  } = useInsuranceStore();

  return {
    bulkAdherenceReport: bulkAdherenceReport || null,
    isLoading: bulkReportLoading || false,
    error: bulkReportError || null,
    hasError: !!bulkReportError,
    fetchBulkAdherenceReport: fetchBulkAdherenceReport || (() => {}),
    refreshBulkReport: (patientIds: string[]) =>
      fetchBulkAdherenceReport ? fetchBulkAdherenceReport({ patient_ids: patientIds, days: 30, include_details: true }) : Promise.resolve(),
  };
}

// Hook for managing risk assessment
export function useRiskAssessment() {
  const {
    riskAssessmentReport,
    riskAssessmentLoading,
    riskAssessmentError,
    fetchRiskAssessmentReport,
  } = useInsuranceStore();

  return {
    riskAssessmentReport: riskAssessmentReport || null,
    isLoading: riskAssessmentLoading || false,
    error: riskAssessmentError || null,
    hasError: !!riskAssessmentError,
    fetchRiskAssessmentReport: fetchRiskAssessmentReport || (() => {}),
    refreshRiskAssessment: (query?: RiskAssessmentQueryDto) => fetchRiskAssessmentReport ? fetchRiskAssessmentReport(query) : Promise.resolve(),
  };
}

// Hook for managing hospital performance
export function useHospitalPerformance() {
  const {
    hospitalPerformance,
    hospitalPerformanceLoading,
    hospitalPerformanceError,
    fetchHospitalPerformance,
  } = useInsuranceStore();

  return {
    hospitalPerformance: hospitalPerformance || [],
    isLoading: hospitalPerformanceLoading || false,
    error: hospitalPerformanceError || null,
    hasError: !!hospitalPerformanceError,
    fetchHospitalPerformance: fetchHospitalPerformance || (() => {}),
    refreshHospitalPerformance: () => fetchHospitalPerformance ? fetchHospitalPerformance() : Promise.resolve(),
  };
}

// Hook for managing adherence analytics
export function useInsuranceAnalytics() {
  const {
    adherenceAnalytics,
    analyticsLoading,
    analyticsError,
    fetchAdherenceAnalytics,
  } = useInsuranceStore();

  return {
    adherenceAnalytics: adherenceAnalytics || null,
    isLoading: analyticsLoading || false,
    error: analyticsError || null,
    hasError: !!analyticsError,
    fetchAdherenceAnalytics: fetchAdherenceAnalytics || (() => {}),
    refreshAnalytics: (params?: { days?: number; group_by?: 'condition' | 'hospital' | 'doctor' }) =>
      fetchAdherenceAnalytics ? fetchAdherenceAnalytics(params) : Promise.resolve(),
  };
}

// Main hook that combines all insurance data management
export function useInsuranceData(options?: {
  autoFetchDashboard?: boolean;
  autoFetchPolicyHolders?: boolean;
  autoFetchHospitalPerformance?: boolean;
  autoFetchAnalytics?: boolean;
  policyHoldersQuery?: PolicyHolderQueryDto;
  analyticsParams?: { days?: number; group_by?: 'condition' | 'hospital' | 'doctor' };
}) {
  const {
    autoFetchDashboard = false,
    autoFetchPolicyHolders = false,
    autoFetchHospitalPerformance = false,
    autoFetchAnalytics = false,
    policyHoldersQuery,
    analyticsParams,
  } = options || {};

  const dashboard = useInsuranceDashboard();
  const policyHolders = usePolicyHolders(policyHoldersQuery);
  const adherenceReports = useAdherenceReports();
  const bulkReport = useBulkAdherenceReport();
  const riskAssessment = useRiskAssessment();
  const hospitalPerformance = useHospitalPerformance();
  const analytics = useInsuranceAnalytics();

  const { clearErrors, reset, refreshAll } = useInsuranceStore();

  // Auto-fetch data on mount if requested
  useEffect(() => {
    if (autoFetchDashboard && !dashboard?.dashboardSummary && !dashboard?.isLoading && dashboard?.fetchDashboardSummary) {
      dashboard.fetchDashboardSummary();
    }
  }, [autoFetchDashboard, dashboard?.dashboardSummary, dashboard?.isLoading, dashboard?.fetchDashboardSummary]);

  useEffect(() => {
    if (autoFetchPolicyHolders && !(policyHolders?.policyHolders?.length) && !policyHolders?.isLoading && policyHolders?.fetchPolicyHolders) {
      policyHolders.fetchPolicyHolders(policyHoldersQuery);
    }
  }, [autoFetchPolicyHolders, policyHolders?.policyHolders?.length, policyHolders?.isLoading, policyHolders?.fetchPolicyHolders, policyHoldersQuery]);

  useEffect(() => {
    if (autoFetchHospitalPerformance && !(hospitalPerformance?.hospitalPerformance?.length) && !hospitalPerformance?.isLoading && hospitalPerformance?.fetchHospitalPerformance) {
      hospitalPerformance.fetchHospitalPerformance();
    }
  }, [autoFetchHospitalPerformance, hospitalPerformance?.hospitalPerformance?.length, hospitalPerformance?.isLoading, hospitalPerformance?.fetchHospitalPerformance]);

  useEffect(() => {
    if (autoFetchAnalytics && !analytics?.adherenceAnalytics && !analytics?.isLoading && analytics?.fetchAdherenceAnalytics) {
      analytics.fetchAdherenceAnalytics(analyticsParams);
    }
  }, [autoFetchAnalytics, analytics?.adherenceAnalytics, analytics?.isLoading, analytics?.fetchAdherenceAnalytics, analyticsParams]);

  // Combined loading state
  const isLoading = dashboard?.isLoading || policyHolders?.isLoading || hospitalPerformance?.isLoading || analytics?.isLoading || false;

  // Combined error state
  const hasError = dashboard?.hasError || policyHolders?.hasError || hospitalPerformance?.hasError || analytics?.hasError || false;
  const error = dashboard?.error || policyHolders?.error || hospitalPerformance?.error || analytics?.error || null;

  return {
    // Dashboard
    dashboardSummary: dashboard?.dashboardSummary || null,
    isDashboardLoading: dashboard?.isLoading || false,
    dashboardError: dashboard?.error || null,
    hasDashboardError: dashboard?.hasError || false,
    refreshDashboard: dashboard?.refreshDashboard || (() => {}),

    // Policy Holders
    policyHolders: policyHolders?.policyHolders || [],
    policyHoldersTotal: policyHolders?.total || 0,
    policyHoldersPage: policyHolders?.page || 1,
    policyHoldersLimit: policyHolders?.limit || 20,
    isPolicyHoldersLoading: policyHolders?.isLoading || false,
    policyHoldersError: policyHolders?.error || null,
    hasPolicyHoldersError: policyHolders?.hasError || false,
    refreshPolicyHolders: policyHolders?.refreshPolicyHolders || (() => {}),

    // Adherence Reports
    adherenceReports: adherenceReports?.adherenceReports || [],
    adherenceReportsLoading: adherenceReports?.adherenceReportsLoading || false,
    adherenceReportsError: adherenceReports?.adherenceReportsError || null,
    fetchPatientAdherenceReport: adherenceReports?.fetchPatientAdherenceReport || (() => {}),
    getPatientReport: adherenceReports?.getPatientReport || (() => null),
    isPatientReportLoading: adherenceReports?.isPatientReportLoading || false,
    getPatientReportError: adherenceReports?.getPatientReportError || null,

    // Bulk Report
    bulkAdherenceReport: bulkReport?.bulkAdherenceReport || null,
    isBulkReportLoading: bulkReport?.isLoading || false,
    bulkReportError: bulkReport?.error || null,
    hasBulkReportError: bulkReport?.hasError || false,
    fetchBulkAdherenceReport: bulkReport?.fetchBulkAdherenceReport || (() => {}),
    refreshBulkReport: bulkReport?.refreshBulkReport || (() => {}),

    // Risk Assessment
    riskAssessmentReport: riskAssessment?.riskAssessmentReport || null,
    isRiskAssessmentLoading: riskAssessment?.isLoading || false,
    riskAssessmentError: riskAssessment?.error || null,
    hasRiskAssessmentError: riskAssessment?.hasError || false,
    fetchRiskAssessmentReport: riskAssessment?.fetchRiskAssessmentReport || (() => {}),
    refreshRiskAssessment: riskAssessment?.refreshRiskAssessment || (() => {}),

    // Hospital Performance
    hospitalPerformance: hospitalPerformance?.hospitalPerformance || [],
    isHospitalPerformanceLoading: hospitalPerformance?.isLoading || false,
    hospitalPerformanceError: hospitalPerformance?.error || null,
    hasHospitalPerformanceError: hospitalPerformance?.hasError || false,
    refreshHospitalPerformance: hospitalPerformance?.refreshHospitalPerformance || (() => {}),

    // Analytics
    adherenceAnalytics: analytics?.adherenceAnalytics || null,
    isAnalyticsLoading: analytics?.isLoading || false,
    analyticsError: analytics?.error || null,
    hasAnalyticsError: analytics?.hasError || false,
    refreshAnalytics: analytics?.refreshAnalytics || (() => {}),

    // Combined states
    isLoading,
    hasError,
    error,

    // Actions
    refreshAll,
    clearErrors,
    reset,
  };
}
