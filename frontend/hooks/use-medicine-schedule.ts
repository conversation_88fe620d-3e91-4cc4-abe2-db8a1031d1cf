'use client';

import { useState, useEffect, useCallback } from 'react';
import { MedicineService } from '@/lib/api/medicines';
import type { Medicine } from '@/types';

interface NextDose {
  medicine: Medicine;
  nextDoseTime: string;
  isOverdue: boolean;
}

interface MedicineScheduleData {
  nextDose: NextDose | null;
  loading: boolean;
  error: string | null;
}

export function useMedicineSchedule() {
  const [data, setData] = useState<MedicineScheduleData>({
    nextDose: null,
    loading: true,
    error: null,
  });

  const fetchNextDose = useCallback(async () => {
    try {
      setData(prev => ({ ...prev, loading: true, error: null }));
      const nextDose = await MedicineService.getNextDose();
      setData({
        nextDose,
        loading: false,
        error: null,
      });
    } catch (error) {
      console.error('Error fetching next dose:', error);
      setData({
        nextDose: null,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch next dose',
      });
    }
  }, []);

  const refreshSchedule = useCallback(() => {
    fetchNextDose();
  }, [fetchNextDose]);

  useEffect(() => {
    fetchNextDose();
  }, [fetchNextDose]);

  return {
    ...data,
    refreshSchedule,
  };
}

/**
 * Calculate next dose time based on frequency
 */
export function calculateNextDoseTime(frequency: string, lastTakenTime: Date): Date {
  const freq = frequency.toLowerCase().trim();
  const nextDose = new Date(lastTakenTime);

  // Calculate interval based on frequency
  if (freq.includes('once daily') || freq.includes('once a day') || freq.includes('1 time') || freq === 'daily') {
    nextDose.setDate(nextDose.getDate() + 1);
    nextDose.setHours(9, 0, 0, 0); // Next day at 9:00 AM
  } else if (freq.includes('twice daily') || freq.includes('twice a day') || freq.includes('2 times') || freq.includes('bid')) {
    nextDose.setHours(nextDose.getHours() + 12); // Every 12 hours
  } else if (freq.includes('three times') || freq.includes('3 times') || freq.includes('tid') || freq.includes('thrice')) {
    nextDose.setHours(nextDose.getHours() + 8); // Every 8 hours
  } else if (freq.includes('four times') || freq.includes('4 times') || freq.includes('qid')) {
    nextDose.setHours(nextDose.getHours() + 6); // Every 6 hours
  } else if (freq.includes('every 6 hours') || freq.includes('q6h')) {
    nextDose.setHours(nextDose.getHours() + 6);
  } else if (freq.includes('every 8 hours') || freq.includes('q8h')) {
    nextDose.setHours(nextDose.getHours() + 8);
  } else if (freq.includes('every 12 hours') || freq.includes('q12h')) {
    nextDose.setHours(nextDose.getHours() + 12);
  } else {
    // Default to once daily
    nextDose.setDate(nextDose.getDate() + 1);
    nextDose.setHours(9, 0, 0, 0);
  }

  return nextDose;
}

/**
 * Parse medication frequency to get scheduled times
 */
export function parseMedicationFrequency(frequency: string): Array<{ hour: number; minute: number }> {
  const freq = frequency.toLowerCase().trim();

  // Common frequency patterns
  if (freq.includes('once daily') || freq.includes('once a day') || freq.includes('1 time') || freq === 'daily') {
    return [{ hour: 9, minute: 0 }]; // 9:00 AM
  }

  if (freq.includes('twice daily') || freq.includes('twice a day') || freq.includes('2 times') || freq.includes('bid')) {
    return [
      { hour: 9, minute: 0 },   // 9:00 AM
      { hour: 21, minute: 0 }   // 9:00 PM
    ];
  }

  if (freq.includes('three times') || freq.includes('3 times') || freq.includes('tid') || freq.includes('thrice')) {
    return [
      { hour: 8, minute: 0 },   // 8:00 AM
      { hour: 14, minute: 0 },  // 2:00 PM
      { hour: 20, minute: 0 }   // 8:00 PM
    ];
  }

  if (freq.includes('four times') || freq.includes('4 times') || freq.includes('qid')) {
    return [
      { hour: 8, minute: 0 },   // 8:00 AM
      { hour: 12, minute: 0 },  // 12:00 PM
      { hour: 16, minute: 0 },  // 4:00 PM
      { hour: 20, minute: 0 }   // 8:00 PM
    ];
  }

  if (freq.includes('every 6 hours') || freq.includes('q6h')) {
    return [
      { hour: 6, minute: 0 },   // 6:00 AM
      { hour: 12, minute: 0 },  // 12:00 PM
      { hour: 18, minute: 0 },  // 6:00 PM
      { hour: 0, minute: 0 }    // 12:00 AM
    ];
  }

  if (freq.includes('every 8 hours') || freq.includes('q8h')) {
    return [
      { hour: 8, minute: 0 },   // 8:00 AM
      { hour: 16, minute: 0 },  // 4:00 PM
      { hour: 0, minute: 0 }    // 12:00 AM
    ];
  }

  if (freq.includes('every 12 hours') || freq.includes('q12h')) {
    return [
      { hour: 8, minute: 0 },   // 8:00 AM
      { hour: 20, minute: 0 }   // 8:00 PM
    ];
  }

  // Default to once daily
  return [{ hour: 9, minute: 0 }]; // 9:00 AM
}

/**
 * Get the next scheduled dose time for a medicine
 */
export function getNextScheduledTime(medicine: Medicine, lastTakenTime?: Date): Date {
  const reminderTimes = parseMedicationFrequency(medicine.frequency);
  const now = new Date();
  
  if (lastTakenTime) {
    // Calculate next dose based on last taken time
    return calculateNextDoseTime(medicine.frequency, lastTakenTime);
  }
  
  // No previous dose, find next scheduled time
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  
  // Find the next scheduled time today
  for (const time of reminderTimes) {
    const scheduledTime = new Date(today);
    scheduledTime.setHours(time.hour, time.minute, 0, 0);
    
    if (scheduledTime > now) {
      return scheduledTime;
    }
  }
  
  // All times for today have passed, use first time tomorrow
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(reminderTimes[0].hour, reminderTimes[0].minute, 0, 0);
  return tomorrow;
}
