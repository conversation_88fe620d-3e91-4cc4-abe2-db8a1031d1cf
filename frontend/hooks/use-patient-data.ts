import { useEffect, useState } from 'react';
import { useAuthStore, useMedicineStore, useGamificationStore } from '@/lib/store';

/**
 * Custom hook to initialize and manage patient dashboard data
 * Fetches all necessary data when component mounts and provides loading states
 */
export function usePatientData() {
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);

  // Store hooks
  const { user, isAuthenticated, checkAuth } = useAuthStore();
  const { 
    medicines, 
    reminders, 
    adherenceRecords, 
    isLoading: medicineLoading,
    error: medicineError,
    fetchMedicines,
    fetchReminders,
    fetchAdherenceRecords,
    getTodaysReminders,
    getUpcomingReminders,
    getAdherenceRate,
    clearError: clearMedicineError
  } = useMedicineStore();
  
  const {
    stats,
    isLoading: gamificationLoading,
    error: gamificationError,
    fetchStats,
    clearError: clearGamificationError
  } = useGamificationStore();

  // Initialize all patient data
  const initializeData = async () => {
    try {
      setIsInitializing(true);
      setInitError(null);

      // Check authentication first
      if (!isAuthenticated) {
        await checkAuth();
      }

      // Fetch all patient data in parallel
      await Promise.all([
        fetchMedicines(),
        fetchReminders(),
        fetchAdherenceRecords(),
        fetchStats(),
      ]);

    } catch (error: any) {
      console.error('Failed to initialize patient data:', error);
      setInitError(error.message || 'Failed to load patient data');
    } finally {
      setIsInitializing(false);
    }
  };

  // Initialize data on mount
  useEffect(() => {
    initializeData();
  }, []);

  // Computed values for dashboard
  const todaysReminders = getTodaysReminders();
  const upcomingReminders = getUpcomingReminders();
  const adherenceRate = getAdherenceRate(7); // Last 7 days

  // Filter active medicines based on start_date and end_date
  const today = new Date();
  const safeMedicines = medicines || [];
  const activeMedicines = safeMedicines.filter(m => {
    if (!m.startDate || !m.endDate) return true; // Include if dates are missing
    const startDate = new Date(m.startDate);
    const endDate = new Date(m.endDate);
    return startDate <= today && endDate >= today;
  });

  // Loading states
  const isLoading = isInitializing || medicineLoading || gamificationLoading;
  const hasError = initError || medicineError || gamificationError;

  // Error handling
  const clearAllErrors = () => {
    setInitError(null);
    clearMedicineError();
    clearGamificationError();
  };

  // Refresh data
  const refreshData = () => {
    initializeData();
  };

  return {
    // User data
    user,
    isAuthenticated,
    
    // Medicine data
    medicines: safeMedicines, // Return all medicines, let components filter as needed
    activeMedicines, // Also provide filtered active medicines
    reminders,
    adherenceRecords,
    todaysReminders,
    upcomingReminders,
    adherenceRate,
    
    // Gamification data
    stats,
    
    // Loading states
    isLoading,
    isInitializing,
    
    // Error handling
    hasError,
    error: hasError ? (initError || medicineError || gamificationError) : null,
    clearAllErrors,
    
    // Actions
    refreshData,
  };
}

/**
 * Hook for patient dashboard stats
 * Provides computed statistics for dashboard cards
 */
export function usePatientStats() {
  const { medicines, adherenceRate, stats } = usePatientData();

  // Safely handle undefined medicines array
  const safeMedicines = medicines || [];

  // Calculate additional stats
  const activeMedicinesCount = safeMedicines.length;
  const medicinesEndingSoon = safeMedicines.filter(m => {
    // Check if medicine ends within a week (duration <= 7 days)
    return m.duration && m.duration <= 7;
  }).length;

  return {
    adherenceRate,
    currentStreak: stats.currentStreak,
    longestStreak: stats.longestStreak,
    totalPoints: stats.totalPoints,
    activeMedicinesCount,
    medicinesEndingSoon,
    
    // Trend indicators (mock for now, can be calculated from historical data)
    adherenceTrend: '+5%', // This would come from comparing periods
    pointsToday: 25, // This would come from today's activities
  };
}
