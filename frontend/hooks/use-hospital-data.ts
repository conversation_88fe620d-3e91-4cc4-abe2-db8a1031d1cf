import { useEffect, useMemo } from 'react';
import { useHospitalStore } from '@/lib/stores/hospital-store';
import type {
  HospitalPatient,
  HospitalDoctor,
  HospitalDashboardSummary,
  HospitalPatientsQuery,
  HospitalDoctorsQuery,
} from '@/lib/api/hospitals';

/**
 * Hook for managing hospital dashboard data
 */
export function useHospitalDashboard() {
  const {
    dashboardSummary,
    isLoadingDashboard,
    dashboardError,
    fetchDashboardSummary,
  } = useHospitalStore();

  const refreshDashboard = () => {
    fetchDashboardSummary();
  };

  return {
    dashboardSummary,
    isLoading: isLoadingDashboard,
    error: dashboardError,
    hasError: !!dashboardError,
    refreshDashboard,
    fetchDashboardSummary,
  };
}

/**
 * Hook for managing hospital patients data
 */
export function useHospitalPatients(query?: HospitalPatientsQuery) {
  const {
    patients,
    patientsTotal,
    patientsPage,
    patientsLimit,
    isLoadingPatients,
    patientsError,
    fetchPatients,
  } = useHospitalStore();

  const refreshPatients = () => {
    fetchPatients(query);
  };

  // Memoized patient statistics
  const patientStats = useMemo(() => {
    // Safely handle undefined patients array
    const safePatients = patients || [];
    if (!safePatients.length) {
      return {
        totalPatients: 0,
        highRiskCount: 0,
        averageAdherence: 0,
        departmentCounts: {},
      };
    }

    const departmentCounts: Record<string, number> = {};

    safePatients.forEach((patient) => {
      const department = patient.assigned_doctor.specialization;
      departmentCounts[department] = (departmentCounts[department] || 0) + 1;
    });

    return {
      totalPatients: safePatients.length,
      highRiskCount: 0, // This would need to be calculated from adherence data
      averageAdherence: 0, // This would need to be calculated from adherence data
      departmentCounts,
    };
  }, [patients]);

  return {
    patients,
    total: patientsTotal,
    page: patientsPage,
    limit: patientsLimit,
    isLoading: isLoadingPatients,
    error: patientsError,
    hasError: !!patientsError,
    patientStats,
    refreshPatients,
    fetchPatients,
  };
}

/**
 * Hook for managing hospital doctors data
 */
export function useHospitalDoctors(query?: HospitalDoctorsQuery) {
  const {
    doctors,
    doctorsTotal,
    doctorsPage,
    doctorsLimit,
    isLoadingDoctors,
    doctorsError,
    fetchDoctors,
  } = useHospitalStore();

  const refreshDoctors = () => {
    fetchDoctors(query);
  };

  // Calculate doctor statistics
  const doctorStats = useMemo(() => {
    if (!doctors || doctors.length === 0) {
      return {
        totalDoctors: 0,
        activeDoctors: 0,
        averageRating: 0,
        specializationCounts: {},
      };
    }

    const specializationCounts: Record<string, number> = {};
    let totalRating = 0;
    let ratedDoctors = 0;
    let activeDoctors = 0;

    doctors.forEach((doctor) => {
      const specialization = doctor.specialization;
      specializationCounts[specialization] = (specializationCounts[specialization] || 0) + 1;

      if (doctor.status === 'active') {
        activeDoctors++;
      }

      if (doctor.rating && doctor.rating > 0) {
        totalRating += doctor.rating;
        ratedDoctors++;
      }
    });

    return {
      totalDoctors: doctors.length,
      activeDoctors,
      averageRating: ratedDoctors > 0 ? totalRating / ratedDoctors : 0,
      specializationCounts,
    };
  }, [doctors]);

  return {
    doctors,
    total: doctorsTotal,
    page: doctorsPage,
    limit: doctorsLimit,
    isLoading: isLoadingDoctors,
    error: doctorsError,
    hasError: !!doctorsError,
    doctorStats,
    refreshDoctors,
    fetchDoctors,
  };
}

/**
 * Hook for managing patient adherence reports
 */
export function usePatientAdherence(patientId?: string) {
  const {
    adherenceReports,
    isLoadingAdherence,
    adherenceError,
    fetchPatientAdherenceReport,
  } = useHospitalStore();

  const adherenceReport = patientId ? adherenceReports[patientId] : null;

  const fetchReport = (id: string, query?: any) => {
    fetchPatientAdherenceReport(id, query);
  };

  return {
    adherenceReport,
    adherenceReports,
    isLoading: isLoadingAdherence,
    error: adherenceError,
    hasError: !!adherenceError,
    fetchReport,
    fetchPatientAdherenceReport,
  };
}

/**
 * Hook for managing bulk adherence reports
 */
export function useBulkAdherence() {
  const {
    bulkAdherenceReport,
    isLoadingBulkAdherence,
    bulkAdherenceError,
    fetchBulkAdherenceReport,
  } = useHospitalStore();

  return {
    bulkAdherenceReport,
    isLoading: isLoadingBulkAdherence,
    error: bulkAdherenceError,
    hasError: !!bulkAdherenceError,
    fetchBulkAdherenceReport,
  };
}

/**
 * Hook for managing department analysis
 */
export function useDepartmentAnalysis(department?: string) {
  const {
    departmentAnalyses,
    isLoadingDepartmentAnalysis,
    departmentAnalysisError,
    fetchDepartmentAnalysis,
  } = useHospitalStore();

  const departmentAnalysis = department ? departmentAnalyses[department] : null;

  const fetchAnalysis = (dept: string, query?: any) => {
    fetchDepartmentAnalysis({ department: dept, ...query });
  };

  return {
    departmentAnalysis,
    departmentAnalyses,
    isLoading: isLoadingDepartmentAnalysis,
    error: departmentAnalysisError,
    hasError: !!departmentAnalysisError,
    fetchAnalysis,
    fetchDepartmentAnalysis,
  };
}

/**
 * Hook for managing patient summaries
 */
export function usePatientSummary(patientId?: string) {
  const {
    patientSummaries,
    isLoadingPatientSummary,
    patientSummaryError,
    fetchPatientSummary,
  } = useHospitalStore();

  const patientSummary = patientId ? patientSummaries[patientId] : null;

  const fetchSummary = (id: string) => {
    fetchPatientSummary(id);
  };

  return {
    patientSummary,
    patientSummaries,
    isLoading: isLoadingPatientSummary,
    error: patientSummaryError,
    hasError: !!patientSummaryError,
    fetchSummary,
    fetchPatientSummary,
  };
}

/**
 * Hook for comprehensive hospital analytics
 */
export function useHospitalAnalytics(options: {
  autoFetch?: boolean;
  timeRange?: string;
  departments?: string[];
} = {}) {
  const {
    dashboardSummary,
    isLoadingDashboard,
    dashboardError,
    fetchDashboardSummary,
    departmentAnalyses,
    isLoadingDepartmentAnalysis,
    departmentAnalysisError,
    fetchDepartmentAnalysis,
    bulkAdherenceReport,
    isLoadingBulkAdherence,
    bulkAdherenceError,
    fetchBulkAdherenceReport,
    patients,
    doctors,
  } = useHospitalStore();

  const { autoFetch = false, timeRange = '6months', departments = [] } = options;

  // Auto-fetch analytics data
  useEffect(() => {
    if (autoFetch) {
      fetchDashboardSummary();

      // Fetch department analyses for specified departments
      departments.forEach(dept => {
        fetchDepartmentAnalysis({
          department: dept,
          days: timeRange === '1month' ? 30 : timeRange === '3months' ? 90 : 180,
          include_doctor_breakdown: true,
          include_trends: true,
        });
      });

      // Fetch bulk adherence report
      const safePatients = patients || [];
      if (safePatients.length > 0) {
        fetchBulkAdherenceReport({
          patient_ids: safePatients.slice(0, 100).map(p => p.patient_id), // Limit to first 100 patients
          days: timeRange === '1month' ? 30 : timeRange === '3months' ? 90 : 180,
          include_detailed_reports: true,
          group_by_department: true,
        });
      }
    }
  }, [autoFetch, timeRange, departments.join(','), (patients || []).length]);

  // Calculate analytics metrics
  const analyticsMetrics = useMemo(() => {
    if (!dashboardSummary) return null;

    const totalPatients = dashboardSummary.overview.total_patients;
    const totalDoctors = dashboardSummary.overview.total_doctors;
    const avgAdherence = dashboardSummary.adherence_metrics.overall_adherence_rate;
    const totalRevenue = 0; // Financial data not available in current dashboard summary

    // Department performance from analyses
    const departmentPerformance = Object.values(departmentAnalyses).map(analysis => ({
      department: analysis.department,
      patients: analysis.metrics.total_patients,
      adherence: Math.round(analysis.metrics.average_adherence_rate),
      revenue: 0, // Financial data not available
      doctors: analysis.metrics.total_doctors,
      satisfaction: 0, // Satisfaction data not available
    }));

    // Risk distribution from bulk adherence report
    const riskDistribution = bulkAdherenceReport?.patient_summaries ? (() => {
      const riskCounts = bulkAdherenceReport.patient_summaries.reduce((acc, patient) => {
        acc[patient.risk_level] = (acc[patient.risk_level] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return [
        {
          name: 'Low Risk',
          value: Math.round(((riskCounts.low || 0) / totalPatients) * 100),
          color: '#10B981',
          patients: riskCounts.low || 0,
        },
        {
          name: 'Medium Risk',
          value: Math.round(((riskCounts.medium || 0) / totalPatients) * 100),
          color: '#F59E0B',
          patients: riskCounts.medium || 0,
        },
        {
          name: 'High Risk',
          value: Math.round(((riskCounts.high || 0) / totalPatients) * 100),
          color: '#EF4444',
          patients: riskCounts.high || 0,
        },
        {
          name: 'Critical Risk',
          value: Math.round(((riskCounts.critical || 0) / totalPatients) * 100),
          color: '#DC2626',
          patients: riskCounts.critical || 0,
        },
      ];
    })() : [];

    return {
      totalPatients,
      totalDoctors,
      avgAdherence,
      totalRevenue,
      departmentPerformance,
      riskDistribution,
    };
  }, [dashboardSummary, departmentAnalyses, bulkAdherenceReport]);

  return {
    // Raw data
    dashboardSummary,
    departmentAnalyses,
    bulkAdherenceReport,

    // Computed analytics
    analyticsMetrics,

    // Loading states
    isLoading: isLoadingDashboard || isLoadingDepartmentAnalysis || isLoadingBulkAdherence,
    isLoadingDashboard,
    isLoadingDepartmentAnalysis,
    isLoadingBulkAdherence,

    // Error states
    error: dashboardError || departmentAnalysisError || bulkAdherenceError,
    hasError: !!(dashboardError || departmentAnalysisError || bulkAdherenceError),
    dashboardError,
    departmentAnalysisError,
    bulkAdherenceError,

    // Actions
    fetchDashboardSummary,
    fetchDepartmentAnalysis,
    fetchBulkAdherenceReport,
    refreshAnalytics: () => {
      fetchDashboardSummary();
      departments.forEach(dept => {
        fetchDepartmentAnalysis({
          department: dept,
          days: timeRange === '1month' ? 30 : timeRange === '3months' ? 90 : 180,
          include_doctor_breakdown: true,
          include_trends: true,
        });
      });
    },
  };
}

/**
 * Main hook that combines all hospital data management
 */
export function useHospitalData(options?: {
  autoFetchDashboard?: boolean;
  autoFetchPatients?: boolean;
  autoFetchDoctors?: boolean;
  patientsQuery?: HospitalPatientsQuery;
  doctorsQuery?: HospitalDoctorsQuery;
}) {
  const {
    autoFetchDashboard = false,
    autoFetchPatients = false,
    autoFetchDoctors = false,
    patientsQuery,
    doctorsQuery,
  } = options || {};

  const dashboard = useHospitalDashboard();
  const patients = useHospitalPatients(patientsQuery);
  const doctors = useHospitalDoctors(doctorsQuery);
  const adherence = usePatientAdherence();
  const bulkAdherence = useBulkAdherence();
  const departmentAnalysis = useDepartmentAnalysis();
  const patientSummary = usePatientSummary();

  const { clearErrors, reset } = useHospitalStore();

  // Auto-fetch data on mount if requested
  useEffect(() => {
    if (autoFetchDashboard && !dashboard.dashboardSummary && !dashboard.isLoading) {
      dashboard.fetchDashboardSummary();
    }
  }, [autoFetchDashboard, dashboard.dashboardSummary, dashboard.isLoading, dashboard.fetchDashboardSummary]);

  useEffect(() => {
    const safePatients = patients.patients || [];
    if (autoFetchPatients && !safePatients.length && !patients.isLoading) {
      patients.fetchPatients(patientsQuery);
    }
  }, [autoFetchPatients, (patients.patients || []).length, patients.isLoading, patients.fetchPatients, patientsQuery]);

  useEffect(() => {
    const safeDoctors = doctors.doctors || [];
    if (autoFetchDoctors && !safeDoctors.length && !doctors.isLoading) {
      doctors.fetchDoctors(doctorsQuery);
    }
  }, [autoFetchDoctors, (doctors.doctors || []).length, doctors.isLoading, doctors.fetchDoctors, doctorsQuery]);

  // Combined loading state
  const isLoading = dashboard.isLoading || patients.isLoading || doctors.isLoading;

  // Combined error state
  const hasError = dashboard.hasError || patients.hasError || doctors.hasError;
  const error = dashboard.error || patients.error || doctors.error;

  // Refresh all data
  const refreshAll = () => {
    dashboard.refreshDashboard();
    patients.refreshPatients();
    doctors.refreshDoctors();
  };

  return {
    // Dashboard
    dashboardSummary: dashboard.dashboardSummary,
    isDashboardLoading: dashboard.isLoading,
    dashboardError: dashboard.error,
    hasDashboardError: dashboard.hasError,
    refreshDashboard: dashboard.refreshDashboard,

    // Patients
    patients: patients.patients,
    patientsTotal: patients.total,
    patientsPage: patients.page,
    patientsLimit: patients.limit,
    isPatientsLoading: patients.isLoading,
    patientsError: patients.error,
    hasPatientsError: patients.hasError,
    patientStats: patients.patientStats,
    refreshPatients: patients.refreshPatients,

    // Doctors
    doctors: doctors.doctors,
    doctorsTotal: doctors.total,
    doctorsPage: doctors.page,
    doctorsLimit: doctors.limit,
    isDoctorsLoading: doctors.isLoading,
    doctorsError: doctors.error,
    hasDoctorsError: doctors.hasError,
    doctorStats: doctors.doctorStats,
    refreshDoctors: doctors.refreshDoctors,

    // Adherence
    adherenceReports: adherence.adherenceReports,
    isAdherenceLoading: adherence.isLoading,
    adherenceError: adherence.error,
    hasAdherenceError: adherence.hasError,
    fetchPatientAdherenceReport: adherence.fetchPatientAdherenceReport,

    // Bulk adherence
    bulkAdherenceReport: bulkAdherence.bulkAdherenceReport,
    isBulkAdherenceLoading: bulkAdherence.isLoading,
    bulkAdherenceError: bulkAdherence.error,
    hasBulkAdherenceError: bulkAdherence.hasError,
    fetchBulkAdherenceReport: bulkAdherence.fetchBulkAdherenceReport,

    // Department analysis
    departmentAnalyses: departmentAnalysis.departmentAnalyses,
    isDepartmentAnalysisLoading: departmentAnalysis.isLoading,
    departmentAnalysisError: departmentAnalysis.error,
    hasDepartmentAnalysisError: departmentAnalysis.hasError,
    fetchDepartmentAnalysis: departmentAnalysis.fetchDepartmentAnalysis,

    // Patient summaries
    patientSummaries: patientSummary.patientSummaries,
    isPatientSummaryLoading: patientSummary.isLoading,
    patientSummaryError: patientSummary.error,
    hasPatientSummaryError: patientSummary.hasError,
    fetchPatientSummary: patientSummary.fetchPatientSummary,

    // Combined states
    isLoading,
    hasError,
    error,

    // Actions
    refreshAll,
    clearErrors,
    reset,
  };
}
