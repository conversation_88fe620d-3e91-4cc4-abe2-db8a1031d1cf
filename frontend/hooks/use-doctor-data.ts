import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/store';
import { useDoctorStore } from '@/lib/stores/doctor-store';

/**
 * Custom hook to initialize and manage doctor dashboard data
 * Fetches all necessary data when component mounts and provides loading states
 */
export function useDoctorData() {
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);

  // Auth store
  const { user, isAuthenticated, checkAuth } = useAuthStore();

  // Doctor store
  const {
    _hasHydrated,
    profile,
    patients,
    selectedPatient,
    dashboardStats,
    analytics,
    prescriptions,
    patientMedicines,
    patientAdherence,
    patientAdherenceStats,
    
    // Loading states
    isLoading,
    isLoadingProfile,
    isLoadingPatients,
    isLoadingDashboard,
    isLoadingAnalytics,
    isLoadingPrescriptions,
    isLoadingPatientData,
    
    // Error states
    profileError,
    patientsError,
    dashboardError,
    analyticsError,
    prescriptionsError,
    patientDataError,
    
    // Actions
    fetchProfile,
    fetchPatients,
    fetchDashboardStats,
    fetchAnalytics,
    fetchPrescriptions,
    selectPatient,
    fetchPatientDetails,
    fetchPatientMedicines,
    fetchPatientAdherence,
    fetchPatientAdherenceStats,
    getCriticalPatients,
    getRecentPatients,
    getPendingPrescriptions,
    clearAllErrors,
    refreshAll,
  } = useDoctorStore();

  // Initialize all doctor data
  const initializeData = async () => {
    try {
      setIsInitializing(true);
      setInitError(null);

      // Check authentication first
      if (!isAuthenticated) {
        await checkAuth();
      }

      // Verify user is a doctor
      if (user && user.role !== 'doctor') {
        throw new Error('Access denied: Doctor role required');
      }

      // Fetch all doctor data in parallel
      await Promise.all([
        fetchProfile(),
        fetchPatients(),
        fetchDashboardStats(),
        fetchPrescriptions(),
      ]);

    } catch (error: any) {
      console.error('Failed to initialize doctor data:', error);
      setInitError(error.message || 'Failed to load doctor data');
    } finally {
      setIsInitializing(false);
    }
  };

  // Initialize data on mount
  useEffect(() => {
    if (user?.role === 'doctor') {
      initializeData();
    }
  }, [user?.role]);

  // Computed values for dashboard
  const criticalPatients = getCriticalPatients();
  const recentPatients = getRecentPatients();
  const pendingPrescriptions = getPendingPrescriptions();

  // Overall loading state
  const isLoadingAny = isInitializing || isLoading || isLoadingProfile || 
                      isLoadingPatients || isLoadingDashboard || isLoadingAnalytics;

  // Overall error state
  const hasError = initError || profileError || patientsError || 
                   dashboardError || analyticsError || prescriptionsError;

  // Error handling
  const clearAllErrorsAndInit = () => {
    setInitError(null);
    clearAllErrors();
  };

  // Refresh all data
  const refreshData = () => {
    initializeData();
  };

  return {
    // User data
    user,
    isAuthenticated,
    
    // Doctor profile
    profile,
    
    // Patients data
    patients,
    selectedPatient,
    criticalPatients,
    recentPatients,
    
    // Dashboard data
    dashboardStats,
    analytics,
    
    // Prescriptions data
    prescriptions,
    pendingPrescriptions,
    
    // Patient-specific data (for selected patient)
    patientMedicines,
    patientAdherence,
    patientAdherenceStats,
    
    // Loading states
    isLoading: isLoadingAny,
    isInitializing,
    isLoadingProfile,
    isLoadingPatients,
    isLoadingDashboard,
    isLoadingAnalytics,
    isLoadingPrescriptions,
    isLoadingPatientData,
    
    // Error handling
    hasError: !!hasError,
    error: hasError,
    initError,
    profileError,
    patientsError,
    dashboardError,
    analyticsError,
    prescriptionsError,
    patientDataError,
    clearAllErrors: clearAllErrorsAndInit,
    
    // Actions
    selectPatient,
    fetchPatientDetails,
    fetchPatientMedicines,
    fetchPatientAdherence,
    fetchPatientAdherenceStats,
    refreshData,
    refreshAll,
  };
}

/**
 * Hook for doctor dashboard stats
 * Provides computed statistics for dashboard cards
 */
export function useDoctorStats() {
  const { dashboardStats, patients, analytics } = useDoctorData();
  const { _hasHydrated } = useDoctorStore();

  // Safely handle undefined patients array (can happen during Zustand hydration)
  // Only use patients data if store has been hydrated to prevent race conditions
  const safePatients = (_hasHydrated && patients) ? patients : [];

  // Calculate additional stats with safe fallbacks
  const totalPatients = dashboardStats?.totalPatients || safePatients.length;
  const averageAdherence = dashboardStats?.averageAdherence || 0;
  const criticalPatients = dashboardStats?.criticalPatients || 0;
  const activePatients = dashboardStats?.activePatients || 0;

  // Calculate trends (mock for now, would come from historical data)
  const adherenceTrend = dashboardStats?.monthlyTrend || 0;
  const patientGrowth = '+12%'; // Mock trend
  const prescriptionTrend = '+8%'; // Mock trend

  return {
    totalPatients,
    activePatients,
    averageAdherence,
    criticalPatients,
    todayAppointments: dashboardStats?.todayAppointments || 0,
    pendingPrescriptions: dashboardStats?.pendingPrescriptions || 0,
    
    // Trends
    adherenceTrend,
    patientGrowth,
    prescriptionTrend,
    
    // Analytics
    departmentComparison: analytics?.departmentComparison,
    patientAdherenceData: analytics?.patientAdherence || [],
    medicationEffectiveness: analytics?.medicationEffectiveness || [],
  };
}

/**
 * Hook for managing selected patient data
 */
export function useSelectedPatient() {
  const {
    selectedPatient,
    patientMedicines,
    patientAdherence,
    patientAdherenceStats,
    isLoadingPatientData,
    patientDataError,
    selectPatient,
    fetchPatientDetails,
    fetchPatientMedicines,
    fetchPatientAdherence,
    fetchPatientAdherenceStats,
  } = useDoctorData();

  // Load patient data when patient is selected
  const loadPatientData = async (patientId: string) => {
    await Promise.all([
      fetchPatientMedicines(patientId),
      fetchPatientAdherence(patientId),
      fetchPatientAdherenceStats(patientId),
    ]);
  };

  // Select patient and load their data
  const selectAndLoadPatient = async (patient: any) => {
    selectPatient(patient);
    if (patient) {
      await loadPatientData(patient.id);
    }
  };

  return {
    selectedPatient,
    patientMedicines,
    patientAdherence,
    patientAdherenceStats,
    isLoadingPatientData,
    patientDataError,
    selectPatient: selectAndLoadPatient,
    loadPatientData,
    fetchPatientDetails,
  };
}
