'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@supabase/supabase-js';
import { useAuthStore } from '@/lib/store';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Pill, Loader2 } from 'lucide-react';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function AuthCallbackPage() {
  const router = useRouter();
  const { setUser, setIsAuthenticated } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the session from the URL hash
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Auth callback error:', error);
          setError('Authentication failed. Please try again.');
          return;
        }

        if (data.session) {
          const user = data.session.user;
          
          // Check if this is a new user (first time Google sign up)
          if (user.user_metadata?.iss && !user.user_metadata?.role) {
            // This is a new Google user, we need to create their profile with patient role
            try {
              // Call our backend to create the user profile with patient role
              const response = await fetch('/api/v1/auth/google-signup', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${data.session.access_token}`,
                },
                body: JSON.stringify({
                  name: user.user_metadata?.full_name || user.user_metadata?.name || user.email,
                  email: user.email,
                  role: 'patient',
                  supabaseUserId: user.id,
                }),
              });

              if (!response.ok) {
                throw new Error('Failed to create user profile');
              }

              const userData = await response.json();
              
              // Update auth store with user data
              setUser(userData.user);
              setIsAuthenticated(true);
              
              toast.success('Account created successfully!');
            } catch (profileError) {
              console.error('Profile creation error:', profileError);
              setError('Failed to create user profile. Please try again.');
              return;
            }
          } else {
            // Existing user, just authenticate
            try {
              // Get user data from our backend
              const response = await fetch('/api/v1/auth/me', {
                headers: {
                  'Authorization': `Bearer ${data.session.access_token}`,
                },
              });

              if (response.ok) {
                const userData = await response.json();
                setUser(userData);
                setIsAuthenticated(true);
                toast.success('Login successful!');
              } else {
                throw new Error('Failed to get user data');
              }
            } catch (userError) {
              console.error('User data error:', userError);
              setError('Failed to get user data. Please try again.');
              return;
            }
          }

          // Redirect to patient dashboard (since all Google signups are patients)
          router.push('/dashboard/patient');
        } else {
          setError('No session found. Please try signing in again.');
        }
      } catch (err) {
        console.error('Auth callback error:', err);
        setError('Authentication failed. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    handleAuthCallback();
  }, [router, setUser, setIsAuthenticated]);

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-600 rounded-xl flex items-center justify-center mb-4">
              <Pill className="h-6 w-6 text-white" />
            </div>
            <CardTitle className="text-2xl">Authentication Error</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <button
              onClick={() => router.push('/auth/login')}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Login
            </button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4">
            <Pill className="h-6 w-6 text-white" />
          </div>
          <CardTitle className="text-2xl">Completing Sign Up</CardTitle>
          <CardDescription>
            Please wait while we set up your account...
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
        </CardContent>
      </Card>
    </div>
  );
}
