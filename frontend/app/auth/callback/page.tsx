'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/lib/store';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Pill, Loader2 } from 'lucide-react';

export default function AuthCallbackPage() {
  const router = useRouter();
  const { setUser, setIsAuthenticated } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the session from the URL hash
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Auth callback error:', error);
          setError('Authentication failed. Please try again.');
          return;
        }

        if (data.session) {
          const user = data.session.user;
          const supabaseToken = data.session.access_token;

          // Store the Supabase token temporarily for API calls
          // We'll get our own JWT token from the backend response

          // Use the google-signup endpoint for both new and existing users
          // The backend handles both cases automatically
          try {
            // Call our backend google-signup endpoint (handles both new and existing users)
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1'}/auth/google-signup`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${supabaseToken}`,
              },
              body: JSON.stringify({
                name: user.user_metadata?.full_name || user.user_metadata?.name || user.email,
                email: user.email,
                role: 'patient',
                supabaseUserId: user.id,
              }),
            });

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({}));
              throw new Error(errorData.message || 'Failed to authenticate with Google');
            }

            const userData = await response.json();

            // Store the JWT token from our backend (not the Supabase token)
            if (userData.accessToken) {
              localStorage.setItem('auth-token', userData.accessToken);
              if (userData.refreshToken) {
                localStorage.setItem('refresh-token', userData.refreshToken);
              }
            }

            // Update auth store with user data
            setUser(userData.user);
            setIsAuthenticated(true);

            // Show appropriate success message
            const isNewUser = !userData.user.created_at ||
              new Date(userData.user.created_at).getTime() > (Date.now() - 60000); // Created within last minute

            toast.success(isNewUser ? 'Account created successfully!' : 'Login successful!');

          } catch (authError) {
            console.error('Google authentication error:', authError);
            setError('Failed to authenticate with Google. Please try again.');
            return;
          }

          // Redirect to patient dashboard (since all Google signups are patients)
          router.push('/dashboard/patient');
        } else {
          setError('No session found. Please try signing in again.');
        }
      } catch (err) {
        console.error('Auth callback error:', err);
        setError('Authentication failed. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    handleAuthCallback();
  }, [router, setUser, setIsAuthenticated]);

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-600 rounded-xl flex items-center justify-center mb-4">
              <Pill className="h-6 w-6 text-white" />
            </div>
            <CardTitle className="text-2xl">Authentication Error</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <button
              onClick={() => router.push('/auth/login')}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Login
            </button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4">
            <Pill className="h-6 w-6 text-white" />
          </div>
          <CardTitle className="text-2xl">Completing Sign Up</CardTitle>
          <CardDescription>
            Please wait while we set up your account...
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
        </CardContent>
      </Card>
    </div>
  );
}
