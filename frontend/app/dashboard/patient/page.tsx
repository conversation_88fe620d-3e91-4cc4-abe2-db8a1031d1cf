'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { StatsCard } from '@/components/ui/stats-card';
import { MedicineCard } from '@/components/medicine/medicine-card';
import { StreakCounter } from '@/components/gamification/streak-counter';
import { AdherenceChart } from '@/components/charts/adherence-chart';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/lib/store';
import { usePatientData, usePatientStats } from '@/hooks/use-patient-data';
import { NotificationFeed, RealTimeStatusWidget } from '@/components/realtime/notification-feed';
import { 
  Activity, 
  Clock, 
  Trophy, 
  Plus, 
  Bell, 
  CheckCircle,
  Calendar,
  Pill,
  TrendingUp,
  Zap,
  Target,
  Sparkles
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

export default function PatientDashboard() {
  const { addNotification } = useAppStore();

  // Use API-integrated patient data
  const {
    user,
    medicines,
    todaysReminders,
    upcomingReminders,
    stats,
    isLoading,
    hasError,
    error,
    clearAllErrors,
    refreshData
  } = usePatientData();

  // Use computed stats for dashboard cards
  const {
    adherenceRate,
    currentStreak,
    longestStreak,
    totalPoints,
    activeMedicinesCount,
    medicinesEndingSoon,
    adherenceTrend,
    pointsToday,
    todaysAdherence,
    weeklyProgress,
    monthlyProgress,
    achievements,
    level,
    pointsToNextLevel,
    nextMilestones,
    completionRate
  } = usePatientStats();

  const handleTakeMedicine = async (medicineId: string) => {
    try {
      // Import the medicines service
      const { MedicineService } = await import('@/lib/api/medicines');

      // Mark medicine as taken with current time
      const result = await MedicineService.markMedicineAsTaken(medicineId, {
        takenTime: new Date().toISOString(),
        notes: 'Marked as taken from patient dashboard'
      });

      addNotification({
        type: 'success',
        title: 'Medicine Taken',
        message: result.nextDoseTime
          ? `Great job! Next dose scheduled for ${new Date(result.nextDoseTime).toLocaleString()}`
          : 'Great job! Keep up the good work.',
        read: false,
      });

      // Refresh data to get updated status
      refreshData();
    } catch (error) {
      console.error('Error marking medicine as taken:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to mark medicine as taken. Please try again.',
        read: false,
      });
    }
  };

  const handleSnooze = (medicineId: string) => {
    addNotification({
      type: 'info',
      title: 'Reminder Snoozed',
      message: 'We\'ll remind you again in 15 minutes.',
      read: false,
    });
  };

  // Show loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-muted-foreground">Loading your dashboard...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Show error state
  if (hasError) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4 max-w-md">
            <div className="h-12 w-12 bg-red-100 rounded-full flex items-center justify-center mx-auto">
              <span className="text-red-600 text-xl">⚠️</span>
            </div>
            <h2 className="text-xl font-semibold text-foreground">Unable to Load Dashboard</h2>
            <p className="text-muted-foreground">{error}</p>
            <div className="flex space-x-3 justify-center">
              <Button onClick={refreshData} className="btn-primary">
                Try Again
              </Button>
              <Button onClick={clearAllErrors} variant="outline">
                Dismiss
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/patient', current: true },
          ]}
        />

        {/* Welcome Section */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gradient">
                  Good morning{user?.name ? `, ${user.name.split(' ')[0]}` : ''}! 👋
                </h1>
                <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                  You have {todaysReminders.length} medicines to take today
                </p>
              </div>
            </div>
          </div>
          <div className="flex space-x-3">
            <Link href="/dashboard/patient/prescriptions">
              <Button className="btn-primary w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Add Prescription</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Activity className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{todaysAdherence}%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Today's Adherence</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">{adherenceTrend} from yesterday</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <Trophy className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{currentStreak}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Current Streak</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">Best: {longestStreak} days</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Pill className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{activeMedicinesCount}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active Medicines</p>
                <div className="flex items-center mt-1">
                  <Clock className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">{medicinesEndingSoon} ending this week</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Zap className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{totalPoints}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Points</p>
                <div className="flex items-center mt-1">
                  <Sparkles className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">+{pointsToday} today</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Today's Medicines */}
            <div>
              <div className="flex items-center justify-between mb-4 sm:mb-6">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Pill className="h-4 w-4 text-white" />
                  </div>
                  <h2 className="text-xl sm:text-2xl font-semibold text-foreground">Today's Medicines</h2>
                </div>
                <Link href="/dashboard/patient/medicines">
                  <Button variant="outline" size="sm" className="hover:bg-accent/50">
                    View All
                  </Button>
                </Link>
              </div>
              <div className="grid gap-4">
                {medicines.length === 0 ? (
                  <div className="text-center py-8">
                    <Pill className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />
                    <h3 className="text-lg font-medium text-foreground mb-2">No Active Medicines</h3>
                    <p className="text-muted-foreground mb-4">Add your first prescription to get started</p>
                    <Link href="/dashboard/patient/prescriptions">
                      <Button className="btn-primary">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Prescription
                      </Button>
                    </Link>
                  </div>
                ) : (
                  (() => {
                    // Calculate next dose times and sort medicines by earliest next dose
                    const { getNextScheduledTime } = require('@/hooks/use-medicine-schedule');

                    const medicinesWithNextDose = medicines.map((medicine) => {
                      const nextDose = getNextScheduledTime(medicine, medicine.lastTakenTime);
                      const adherenceRecords = medicine.adherenceRecords || [];
                      const takenRecords = adherenceRecords.filter(r => r.status === 'taken');
                      const adherenceRate = adherenceRecords.length > 0
                        ? Math.round((takenRecords.length / adherenceRecords.length) * 100)
                        : 0;

                      return {
                        medicine,
                        nextDose,
                        adherenceRate,
                        // Sort key: overdue medicines first, then by next dose time
                        sortKey: nextDose ? (nextDose < new Date() ? 0 : nextDose.getTime()) : Infinity
                      };
                    });

                    // Sort by next dose time (overdue first, then earliest next dose)
                    const sortedMedicines = medicinesWithNextDose
                      .sort((a, b) => a.sortKey - b.sortKey)
                      .slice(0, 2); // Show top 2 medicines due next

                    return sortedMedicines.map(({ medicine, nextDose, adherenceRate }) => (
                      <div key={medicine.id} className="modern-card">
                        <MedicineCard
                          medicine={medicine}
                          nextDose={nextDose}
                          adherenceRate={adherenceRate}
                          onTakeMedicine={handleTakeMedicine}
                          onSnooze={handleSnooze}
                        />
                      </div>
                    ));
                  })()
                )}
              </div>
            </div>

            {/* Adherence Chart */}
            <div className="hidden sm:block">
              <div className="modern-card">
                <AdherenceChart
                  title="Weekly Adherence Progress"
                  data={weeklyProgress.map((adherence, index) => ({
                    date: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][index],
                    adherence: Math.round(adherence),
                    target: 100
                  }))}
                />
              </div>
            </div>

            {/* Real-time Notification Feed */}
            <div className="hidden lg:block">
              <NotificationFeed />
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Streak Counter */}
            <div className="modern-card">
              <StreakCounter
                currentStreak={currentStreak}
                longestStreak={longestStreak}
                weeklyProgress={weeklyProgress}
              />
            </div>

            {/* Real-time Status Widget */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Activity className="h-4 w-4 text-white" />
                  </div>
                  <span>Live Updates</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RealTimeStatusWidget />
              </CardContent>
            </Card>

            {/* Upcoming Reminders */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Bell className="h-4 w-4 text-white" />
                  </div>
                  <span>Upcoming Reminders</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {upcomingReminders.length === 0 ? (
                  <div className="text-center py-6">
                    <Bell className="mx-auto h-8 w-8 text-muted-foreground/50 mb-2" />
                    <p className="text-muted-foreground text-sm">No upcoming reminders</p>
                  </div>
                ) : (
                  upcomingReminders.map((reminder) => (
                    <div key={reminder.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-sm truncate">Medicine Name</p>
                        <p className="text-xs text-muted-foreground">
                          {reminder.scheduledTime.toLocaleTimeString([], { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </p>
                      </div>
                      <Badge variant="outline" className="text-xs modern-badge">
                        {reminder.reminderType}
                      </Badge>
                    </div>
                  ))
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Link href="/dashboard/patient/reminders">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Clock className="h-4 w-4 mr-2" />
                    Set Reminder
                  </Button>
                </Link>
                <Link href="/dashboard/patient/progress">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Activity className="h-4 w-4 mr-2" />
                    View Progress
                  </Button>
                </Link>
                <Link href="/dashboard/patient/achievements">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Trophy className="h-4 w-4 mr-2" />
                    Achievements
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Mobile Adherence Chart */}
        <div className="sm:hidden">
          <div className="modern-card">
            <AdherenceChart title="Weekly Adherence Progress" />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}