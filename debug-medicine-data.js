// Debug script to test medicine data and date parsing
const fetch = require('node-fetch');

async function testMedicineAPI() {
  try {
    console.log('🔍 Testing Medicine API Data...\n');
    
    // Test login first
    const loginResponse = await fetch('http://localhost:3001/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('✅ Login successful:', loginData.user?.email);
    
    const token = loginData.access_token;
    
    // Test medicines API
    const medicinesResponse = await fetch('http://localhost:3001/api/v1/medicines', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });
    
    const medicinesData = await medicinesResponse.json();
    console.log('\n📋 Raw Medicine Data from API:');
    console.log(JSON.stringify(medicinesData, null, 2));
    
    if (medicinesData && medicinesData.length > 0) {
      const firstMedicine = medicinesData[0];
      console.log('\n🔬 Analyzing First Medicine:');
      console.log('- Name:', firstMedicine.name);
      console.log('- Start Date (raw):', firstMedicine.start_date);
      console.log('- End Date (raw):', firstMedicine.end_date);
      console.log('- Frequency:', firstMedicine.frequency);
      console.log('- Duration:', firstMedicine.duration);
      
      // Test date parsing
      const startDate = new Date(firstMedicine.start_date);
      const endDate = new Date(firstMedicine.end_date);
      const now = new Date();
      
      console.log('\n📅 Date Analysis:');
      console.log('- Start Date (parsed):', startDate.toISOString());
      console.log('- End Date (parsed):', endDate.toISOString());
      console.log('- Current Date:', now.toISOString());
      console.log('- Days until expiry:', Math.ceil((endDate - now) / (1000 * 60 * 60 * 24)));
      console.log('- Is expired?', endDate < now);
      
      // Test adherence records
      if (firstMedicine.adherence_records) {
        console.log('\n💊 Adherence Records:');
        console.log('- Total records:', firstMedicine.adherence_records.length);
        firstMedicine.adherence_records.forEach((record, index) => {
          console.log(`  Record ${index + 1}:`, {
            status: record.status,
            taken_time: record.taken_time,
            scheduled_time: record.scheduled_time
          });
        });
      } else {
        console.log('\n⚠️  No adherence records found');
      }
    }
    
    // Test next dose API
    console.log('\n🎯 Testing Next Dose API...');
    const nextDoseResponse = await fetch('http://localhost:3001/api/v1/medicines/next-dose', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });
    
    const nextDoseData = await nextDoseResponse.json();
    console.log('Next Dose Data:', JSON.stringify(nextDoseData, null, 2));
    
  } catch (error) {
    console.error('❌ Error testing API:', error.message);
  }
}

testMedicineAPI();
